### 词汇汪 · 开发说明与迭代计划（学生端为主）

本文件用于梳理当前状态、未完成功能以及分阶段实现步骤，便于持续推进与对齐教师端/后端。

### 一、当前已实现（学生端 Flutter）
- 设计系统与主题：宁静蓝/活力黄、圆角、浅灰背景、M3 组件风格（`lib/theme/app_theme.dart`）。
- 框架与导航：底部四页 Tab（学习、复习、报告、我的），路由初始化（`lib/main.dart`、`widgets/app_tab_scaffold.dart`）。
- 主页：顶部透明背景 GIF 虚拟形象、欢迎语、显眼 CTA 按钮、底部两张功能卡（`pages/home_page.dart`）。
- 学习页：学习列表与学习流程、AI 助记与例句占位、笔记块、两轮测验触发与结果页（`pages/learning_list_page.dart`、`pages/learning_study_page.dart`、`pages/study_result_page.dart`）。
- 复习中心：四种模式入口卡片，浅色插画风格（`pages/review_page.dart`）。
- 经典复习（新增）：
  - 题型：单词选释义（英→中）→ 错题再刷一轮（英→中）→ 释义选单词（中→英）；每题底部三态反馈写入 `LocalStore` 与 `SrsStore`（`pages/review_quiz_page.dart`）。
  - 入口：复习中心“经典复习”与首页“复习任务”卡片均可直达（`pages/review_page.dart`、`pages/home_page.dart`）。
  - SRS 到期集合：优先从 `SrsStore.dueWordKeys()` 取到期词；若为空，自动注入“昨日学习30词（随机熟练度）”测试数据。
- 游戏化消消乐（增强）：三关难度递升；配对抖动→缩放淡出→移除；进入结算自动礼花炮与音效；结算显示总用时与词数，首页返回（`pages/review_match_game_page.dart`）。
- 个人页测试入口：调试页可查看部分 SRS 记录、最近反馈，并一键进入经典复习（`pages/profile_page.dart`）。
- SRS 存储：`SrsRecord` 占位实现；提供 `applyFeedback`、`dueWordKeys`、`upsertRecord`、`all` 等（`services/srs_service.dart`、`services/srs_store.dart`）。
- 首页复习统计（新增）：徽章与进度基于 SRS 到期数与当日反馈条数估算显示（`pages/home_page.dart`）。

### 二、与 PRD 对照的未实现/待补充功能（更新）
- 账户与权限
  - 手机号/邮箱登录的真实后端联通、验证码/密码找回、Token 鉴权与刷新。
  - 用户资料编辑、头像上传（裁剪/压缩）。
- 学习流程
  - 发音（TTS）调用与缓存；点击音标播放。
  - 学习进度与会话数据落库（本地 SQLite/Hive + 云端同步）。
  - 三态反馈驱动 SRS 的完整落库与调度（每日任务生成、过期优先、上限顺延）。
  - 词库装载与分册/分级（词频、考试类型、教师任务推送）。
- AI 能力
  - AI 助记：改占位为真实 LLM API（Gemini 或对接服务端），加入重试、超时与去重缓存。
  - AI 每日总结：根据当天学习数据生成个性化总结。
  - AI 短文复习：聚合当日需复习词生成可读短文。
- 复习模式
  - 听音选词、拼写模式；错题本归档与回刷强化；复习进度真实计算（替换“以反馈数估算”）。
  - 游戏化消消乐：基本可玩机制与得分规则。
- 小组/教师联动
  - 加入班级（班级码）、小组 PK 实时积分与我的贡献值，任务下发拉取。
  - 与教师端统一 API 契约（任务、班级、PK、统计）。
- 报告中心
  - 学习日历热力格、正确率与趋势图（本地统计 + 服务端聚合）。
  - 艾宾浩斯曲线拟合图（基于实际回忆数据）。
- 非功能
  - 错误监控与日志上报、埋点（启动、学习开始/完成、AI 请求耗时等）。
  - 深色模式与国际化（中/英）。
  - 打包发布流程（Android/iOS），证书与渠道配置。

### 三、技术方案与依赖（学生端）
- 状态与数据：
  - 本地缓存：优先 Hive（轻量 KV + Box 模式）或 drift/sqflite（关系型）存储学习记录与 SRS 参数。
  - 同步策略：前台触发 + 启动增量拉取；冲突以服务端为准，持久化变更队列（离线可用）。
- 网络与鉴权：
  - HTTP 客户端 `dio`，统一拦截器与重试策略；Token 续期；超时 10s；错误码统一弹层。
- TTS：
  - 先走云端 TTS（服务端签名），App 侧做结果缓存（文件名 hash(word+voice)）。
- LLM 接入：
  - 由服务端统一网关转发（防泄密），App 仅传词、上下文与匿名用户信息；灰度开关与配额控制。
- SRS：
  - 使用 `SrsRecord{n, EF, I, nextDate}`；反馈q=0-5；每日限量+过期优先+Fuzz 扰动；假期模式冻结。

### 四、API 契约（草案，供教师端/后端对齐）
- Auth
  - POST /auth/login, /auth/sendCode, /auth/register, /auth/refresh
- 词与学习
  - GET /words/daily?limit=30&level=...  获取今日学习清单
  - POST /study/session 开始学习会话
  - POST /study/feedback  {wordId, q, ts} 写入反馈（驱动 SRS）
  - GET /study/review-tasks?date=...  拉取复习任务
- AI
  - POST /ai/mnemonic {wordId, lang} -> {text}
  - POST /ai/summary {date} -> {text}
  - POST /ai/paragraph {wordIds[]} -> {richText}
- 班级/PK
  - POST /class/join {code}
  - GET /pk/board?classId=...  排行与我的贡献
- 报告
  - GET /report/overview  指标总览
  - GET /report/calendar?month=...  学习日历

### 五、里程碑与实现步骤（调整）
- M1：本地数据闭环（进行中）
  - [x] 经典复习最小闭环与三态反馈
  - [x] SRS 到期集合计算与首页展示
  - [x] 消消乐三关与结算动画
  - [ ] TTS 最小接入与缓存（学习/复习页面可播音）
  - [ ] 错题本回刷串联至经典复习
- M2：网络与鉴权（未开始）
  - 接入 `dio`、登录/注册/验证码、Token 续期，学习记录与 SRS 同步
- M3：AI 能力接入（未开始）
  - 助记/每日总结/短文复习 API 与缓存；弱网回退
- M4：复习模式扩展与报告（未开始）
  - 听音选词、拼写模式；报告可视化

### 六、任务清单（按模块拆分）
- 基础设施
  - [ ] 集成 Hive/sqflite 与数据表定义（词、学习记录、SRS、设置）
  - [ ] 封装本地仓库与同步管理器
  - [ ] 引入 `dio`、鉴权拦截、通用错误处理

- 学习与复习
  - [ ] 今日学习清单生成（新词配额 + 过期复习）
  - [ ] 学习会话状态机（下一词/上一词/跳过）
  - [ ] 经典复习-看词选义/听音选词/拼写流程
  - [ ] 错题本与回刷

- SRS
  - [ ] 三态反馈映射 q=0/4/5 与公式校验
  - [ ] Fuzz 扰动与每日上限、假期模式
  - [ ] 任务调度（生成 next_review_date 队列）

- AI 与 TTS
  - [ ] TTS 调用与本地音频缓存
  - [ ] 助记/总结/短文 API 接入与缓存、重试

- 小组与教师联动
  - [ ] 班级码加入、教师任务拉取与提交
  - [ ] PK 排行与贡献值上报

- 报告与可视化
  - [ ] 学习日历热力格
  - [ ] 趋势/正确率/艾宾浩斯图

- 质量与交付
  - [ ] 基础埋点、崩溃与日志上报
  - [ ] 深色模式与国际化
  - [ ] 打包与分发流水线

### 七、注意事项
- 资产命名避免中文（Android AssetManager 兼容性），已统一为 ASCII。
- 模型/服务调用全部由服务端网关代理，App 不直接保存密钥；加入速率限制与灰度开关。
- 性能优先：动效使用矢量（Lottie/Rive）或低分辨率 GIF；图片按 `cacheWidth/Height` 裁剪缓存。

### 八、下一步建议（更新）
- 落地 TTS 播放与文件缓存，优先学习页；随后在经典复习中加“读音”干预。
- 复习统计改为真实口径：记录每日完成的“到期词”数量并用于首页进度，不再以反馈数估算。
- 错题本页与经典复习打通：从错题集合发起“错题专项复习”。
- 抽象“数据同步器”，为后续对接教师端/后端做准备。


