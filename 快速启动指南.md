# 《词汇汪》快速启动指南

## 🚀 后端启动步骤

### 1. IDEA中启动后端
1. **打开IDEA**，导入 `backend` 项目
2. **等待依赖下载完成**
3. **检查JDK版本**：File → Project Structure → Project → SDK 选择 JDK 17+
4. **启动应用**：运行 `BackendApplication.java` 的 main 方法
5. **验证启动**：浏览器访问 `http://localhost:8090/api/v1/test/health`

### 2. 导入测试数据
```bash
# 方法1: 使用生成的SQL文件（在IDEA Terminal中执行）
mysql -u root -p cihuiwang < test_words.sql

# 方法2: 使用API接口
curl -X POST "http://localhost:8090/api/v1/admin/import-preset/cet4"
```

---

## 📱 前端启动步骤

### 1. Flutter环境检查
```bash
flutter doctor
```

### 2. 安装依赖并启动
```bash
cd cihuiwang_student
flutter pub get
flutter run
```

---

## ✅ 功能测试清单

### 1. **后端API测试**
- [ ] 访问健康检查：`GET /api/v1/test/health`
- [ ] 词汇搜索：`GET /api/v1/words/search?query=hello`
- [ ] 单词详情：`GET /api/v1/words/detail/hello`
- [ ] 今日词汇：`GET /api/v1/words/daily?limit=5`

### 2. **前端功能测试**
- [ ] 主页搜索框：输入单词，查看下拉结果
- [ ] 搜索结果：点击结果进入详情页
- [ ] 单词详情页：查看标签、词汇变换等信息
- [ ] 发音功能：点击音量按钮播放发音

---

## 🎯 新增功能展示

### **主页搜索功能**
1. **在主页顶部**输入任意英文单词
2. **实时显示搜索结果**，包含音标和释义
3. **点击任意结果**直接跳转到详情页

### **单词详情页面**
进入详情页后可以看到：

```
📝 单词标题
   word [phonetic] 🔊

🌍 中文释义
   详细的中文翻译...

🏷️ 词汇标签 
   [四级] [牛津核心] [柯林斯3星]

🔄 词汇变换
   过去式: worked
   现在分词: working
   第三人称单数: works

ℹ️ 详细信息
   词性: v. 动词
   英文释义: to be engaged in...
   柯林斯星级: 5星
   词频排序: #156
```

### **标签颜色说明**
- 🔴 **核心词汇** - 红色
- 🔵 **四级词汇** - 蓝色
- 🟣 **六级词汇** - 紫色
- 🟠 **托福词汇** - 橙色
- 🟢 **雅思词汇** - 绿色
- 🟡 **牛津核心** - 琥珀色

---

## 🔧 常见问题解决

### **后端编译错误**
如果IDEA显示编译错误：
1. **重新导入项目**：File → Reload Gradle Project
2. **清理缓存**：File → Invalidate Caches and Restart
3. **检查Lombok插件**：File → Settings → Plugins → 确保Lombok已安装
4. **检查注解处理**：File → Settings → Build → Compiler → Annotation Processors → 启用注解处理

### **数据库连接问题**
如果提示数据库连接失败：
1. **确保MySQL运行**：启动MySQL服务
2. **创建数据库**：`CREATE DATABASE cihuiwang;`
3. **检查配置**：修改 `application.yml` 中的数据库连接信息

### **Flutter网络问题**
如果搜索功能不工作：
1. **检查后端状态**：确保后端在8090端口运行
2. **网络权限**：Android需要在 `AndroidManifest.xml` 中添加网络权限
3. **API地址**：检查 `api_service.dart` 中的baseUrl是否正确

---

## 📊 测试数据说明

当前测试数据包含：
- **100个CET4核心词汇**
- **完整的ECDICT信息**：音标、释义、词性、柯林斯星级等
- **词汇变换数据**：过去式、现在分词等语法变换
- **考试标签**：CET4、牛津核心等标签信息

---

## 🎉 体验新功能

1. **启动应用后**，在主页搜索框输入 "work"
2. **查看搜索结果**中的音标 `[wɜːrk]` 和释义
3. **点击结果**进入详情页面
4. **观察标签**：应该显示 `[四级]` `[牛津核心]` 等蓝色和琥珀色标签
5. **查看词汇变换**：过去式 worked，现在分词 working 等
6. **点击发音按钮** 🔊 测试TTS功能

这样就可以完整体验新开发的搜索和详情展示功能了！
