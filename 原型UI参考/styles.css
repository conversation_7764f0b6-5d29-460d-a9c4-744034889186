/* 词汇汪 Design System */
:root {
    --primary: #5B8DEF;
    /* 宁静蓝 */
    --cta: #FFD166;
    /* 活力黄 */
    --bg: #F5F5F5;
    /* 浅灰背景 */
    --text: #1F2937;
    /* 主文本 */
    --muted: #6B7280;
    /* 次文本 */
    --success: #22C55E;
    /* 成功 */
    --warning: #F59E0B;
    /* 提示 */
    --error: #EF4444;
    /* 错误 */
    --card-radius: 16px;
    --shadow-1: 0 4px 16px rgba(17, 24, 39, 0.08);
}

* {
    box-sizing: border-box;
}

html,
body {
    height: 100%;
}

body {
    margin: 0;
    font-family: "Noto Sans SC", "SF Pro SC", "PingFang SC", "Microsoft YaHei", "Helvetica Neue", Arial, sans-serif;
    color: var(--text);
    background: var(--bg);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

img {
    max-width: 100%;
    display: block;
}

a {
    color: inherit;
    text-decoration: none;
}

.container {
    max-width: 720px;
    margin: 0 auto;
    padding: 16px 16px calc(env(safe-area-inset-bottom) + 88px);
}

.topbar {
    position: sticky;
    top: 0;
    z-index: 10;
    background: #fff;
    padding: 14px 16px;
    box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05);
}

.topbar h1 {
    margin: 0;
    font-size: 20px;
}

.subtitle {
    color: var(--muted);
    font-size: 13px;
}

.card {
    background: #fff;
    border-radius: var(--card-radius);
    box-shadow: var(--shadow-1);
    padding: 16px;
}

.card+.card {
    margin-top: 12px;
}

.grid-2 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.section-title {
    margin: 20px 0 10px;
    font-size: 16px;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 14px 18px;
    border-radius: 14px;
    border: 1px solid transparent;
    font-weight: 600;
    cursor: pointer;
    transition: transform .05s ease, box-shadow .2s ease;
}

.btn:active {
    transform: translateY(1px);
}

.btn-primary {
    background: var(--primary);
    color: #fff;
}

.btn-cta {
    background: var(--cta);
    color: #111827;
}

.btn-outline {
    background: #fff;
    border-color: #E5E7EB;
    color: var(--text);
}

.btn-full {
    width: 100%;
}

.badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 22px;
    height: 22px;
    padding: 0 6px;
    border-radius: 999px;
    font-size: 12px;
    background: #111827;
    color: #fff;
}

.row {
    display: flex;
    align-items: center;
    gap: 12px;
}

.space-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.muted {
    color: var(--muted);
}

.hero {
    margin: 16px 0 18px;
}

.hero h2 {
    margin: 0 0 6px;
    font-size: 22px;
}

/* 底部导航 */
.tabbar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    border-top: 1px solid #E5E7EB;
    padding: 8px 8px calc(env(safe-area-inset-bottom) + 8px);
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 4px;
    z-index: 20;
}

.tabbar a {
    text-align: center;
    padding: 8px 6px;
    border-radius: 12px;
    color: var(--muted);
}

.tabbar a.active {
    color: var(--primary);
    background: rgba(91, 141, 239, 0.08);
}

.tabbar i {
    font-size: 20px;
    display: block;
}

.tabbar span {
    font-size: 12px;
}

/* 表单 */
.field {
    display: grid;
    gap: 8px;
}

.input {
    width: 100%;
    padding: 14px 14px;
    border-radius: 14px;
    border: 1px solid #E5E7EB;
    background: #fff;
    outline: none;
}

.input:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(91, 141, 239, 0.15);
}

.tabs {
    display: flex;
    gap: 8px;
    background: #fff;
    padding: 6px;
    border-radius: 12px;
}

.tab {
    flex: 1;
    text-align: center;
    padding: 10px 8px;
    border-radius: 10px;
    color: var(--muted);
    cursor: pointer;
}

.tab.active {
    background: rgba(91, 141, 239, 0.12);
    color: var(--primary);
    font-weight: 600;
}

/* 学习卡片 */
.word-card .word {
    font-size: 28px;
    font-weight: 800;
}

.word-card .phonetic {
    color: var(--muted);
}

.word-card .meaning {
    margin-top: 8px;
}

.word-card .example {
    margin-top: 10px;
    color: var(--muted);
    font-size: 14px;
}

.status-actions {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    margin-top: 16px;
}

.status-btn {
    height: 64px;
    border-radius: 999px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #fff;
    font-weight: 700;
}

.status-btn.error {
    background: var(--error);
}

.status-btn.warning {
    background: var(--warning);
    color: #111827;
}

.status-btn.success {
    background: var(--success);
}

/* 复习中心卡片尺寸 */
.mode-card {
    min-height: 110px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* 进度条 */
.progress {
    width: 100%;
    height: 10px;
    background: #E5E7EB;
    border-radius: 999px;
    overflow: hidden;
}

.progress>span {
    display: block;
    height: 100%;
    background: var(--primary);
}

/* 列表 */
.list {
    background: #fff;
    border-radius: var(--card-radius);
    box-shadow: var(--shadow-1);
    overflow: hidden;
}

.list-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 14px 16px;
    border-bottom: 1px solid #F3F4F6;
}

.list-item:last-child {
    border-bottom: none;
}

.switch {
    position: relative;
    width: 48px;
    height: 28px;
    background: #E5E7EB;
    border-radius: 999px;
    transition: background .2s;
}

.switch input {
    display: none;
}

.switch .thumb {
    position: absolute;
    top: 3px;
    left: 3px;
    width: 22px;
    height: 22px;
    border-radius: 50%;
    background: #fff;
    box-shadow: 0 1px 2px rgba(0, 0, 0, .15);
    transition: left .2s;
}

.switch input:checked+.thumb {
    left: 23px;
}

.switch input:checked~.track {
    background: var(--primary);
}

/* 弹窗 */
.modal-backdrop {
    position: fixed;
    inset: 0;
    background: rgba(17, 24, 39, .45);
    display: none;
    align-items: flex-end;
    z-index: 30;
}

.modal-backdrop.active {
    display: flex;
}

.modal {
    width: 100%;
    max-width: 720px;
    margin: 0 auto;
    background: #fff;
    border-radius: 16px 16px 0 0;
    padding: 16px;
    box-shadow: 0 -8px 24px rgba(0, 0, 0, .15);
}

.modal .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal .modal-body {
    margin-top: 10px;
    color: var(--text);
}

/* 可复用图表容器 */
.chart-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

/* 登录页插画 */
.hero-illust {
    display: flex;
    justify-content: center;
    margin: 12px 0 6px;
}

/* 辅助工具类 */
.mt-8 {
    margin-top: 8px;
}

.mt-12 {
    margin-top: 12px;
}

.mt-16 {
    margin-top: 16px;
}

.mt-20 {
    margin-top: 20px;
}

.mt-24 {
    margin-top: 24px;
}

.mt-32 {
    margin-top: 32px;
}

.center {
    text-align: center;
}

/* 自适应 */
@media (min-width: 768px) {
    .container {
        padding-bottom: 120px;
    }
}