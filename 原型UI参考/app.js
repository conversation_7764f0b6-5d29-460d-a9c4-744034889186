// 词汇汪 原型脚本（无依赖，便于本地预览）

// 图标：使用 Feather icons 的 SVG（开源）
const Icons = {
    home: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 9l9-7 9 7"/><path d="M9 22V12h6v10"/></svg>',
    repeat: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="17 1 21 5 17 9"/><path d="M3 11V9a4 4 0 0 1 4-4h14"/><polyline points="7 23 3 19 7 15"/><path d="M21 13v2a4 4 0 0 1-4 4H3"/></svg>',
    chart: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 3v18h18"/><rect x="7" y="9" width="3" height="7"/><rect x="12" y="5" width="3" height="11"/><rect x="17" y="12" width="3" height="4"/></svg>',
    user: '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/><circle cx="12" cy="7" r="4"/></svg>',
    volume: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"/><path d="M19.07 4.93a10 10 0 0 1 0 14.14"/><path d="M15.54 8.46a5 5 0 0 1 0 7.07"/></svg>',
    sparkles: '<svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 3l2 5 5 2-5 2-2 5-2-5-5-2 5-2 2-5z" transform="translate(8 1)"/></svg>',
    trophy: '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M8 21h8"/><path d="M12 17v4"/><path d="M7 4h10v4a5 5 0 0 1-10 0V4z"/><path d="M5 8a2 2 0 0 1-2-2V5h4"/><path d="M19 8a2 2 0 0 0 2-2V5h-4"/></svg>',
    play: '<svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor"><path d="M8 5v14l11-7z"/></svg>',
    award: '<svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="8" r="7"/><path d="M8.21 13.89L7 23l5-3 5 3-1.21-9.11"/></svg>'
};

function injectTabbar(active) {
    const el = document.querySelector('#tabbar');
    if (!el) return;
    el.innerHTML = `
    <a href="index.html" class="${active === 'home' ? 'active' : ''}">
      <i>${Icons.home}</i><span>学习</span>
    </a>
    <a href="review.html" class="${active === 'review' ? 'active' : ''}">
      <i>${Icons.repeat}</i><span>复习</span>
    </a>
    <a href="report.html" class="${active === 'report' ? 'active' : ''}">
      <i>${Icons.chart}</i><span>报告</span>
    </a>
    <a href="profile.html" class="${active === 'profile' ? 'active' : ''}">
      <i>${Icons.user}</i><span>我的</span>
    </a>
  `;
}

// 简单环形图与折线图（Canvas）用于原型可视化
function drawRingProgress(canvas, percent, color = '#5B8DEF') {
    const ctx = canvas.getContext('2d');
    const dpr = window.devicePixelRatio || 1;
    const size = 160; const stroke = 14;
    canvas.width = size * dpr; canvas.height = size * dpr; canvas.style.width = size + 'px'; canvas.style.height = size + 'px';
    ctx.scale(dpr, dpr);
    ctx.lineWidth = stroke;
    const r = (size - stroke) / 2; const cx = size / 2; const cy = size / 2;
    ctx.strokeStyle = '#E5E7EB';
    ctx.beginPath(); ctx.arc(cx, cy, r, 0, Math.PI * 2); ctx.stroke();
    ctx.strokeStyle = color; ctx.lineCap = 'round';
    ctx.beginPath(); ctx.arc(cx, cy, r, -Math.PI / 2, -Math.PI / 2 + Math.PI * 2 * (percent / 100)); ctx.stroke();
    ctx.fillStyle = '#111827'; ctx.font = '600 18px system-ui, -apple-system, Segoe UI'; ctx.textAlign = 'center'; ctx.textBaseline = 'middle';
    ctx.fillText(percent + '%', cx, cy);
}

function drawLineTrend(canvas, values, color = '#5B8DEF') {
    const ctx = canvas.getContext('2d');
    const dpr = window.devicePixelRatio || 1;
    const w = 260, h = 160, pad = 20;
    canvas.width = w * dpr; canvas.height = h * dpr; canvas.style.width = w + 'px'; canvas.style.height = h + 'px';
    ctx.scale(dpr, dpr);
    ctx.fillStyle = '#fff'; ctx.fillRect(0, 0, w, h);
    const min = Math.min(...values), max = Math.max(...values);
    function y(v) { return h - pad - (v - min) / (max - min || 1) * (h - pad * 2); }
    const step = (w - pad * 2) / (values.length - 1);
    ctx.strokeStyle = '#E5E7EB';
    ctx.beginPath(); ctx.rect(pad, pad, w - pad * 2, h - pad * 2); ctx.stroke();
    ctx.strokeStyle = color; ctx.lineWidth = 2; ctx.beginPath();
    values.forEach((v, i) => {
        const x = pad + i * step; const yy = y(v);
        if (i === 0) ctx.moveTo(x, yy); else ctx.lineTo(x, yy);
    });
    ctx.stroke();
}

// 简易事件：AI助记弹窗
function setupAIMnemonic() {
    const btn = document.querySelector('[data-ai-mnemonic]');
    if (!btn) return;
    const backdrop = document.querySelector('#modal-backdrop');
    const body = document.querySelector('#modal-body');
    const closeBtn = document.querySelector('#modal-close');
    const sample = 'AI助记：记单词 "canine"（犬的），可以联想为中文“看你（canine）家的狗狗”，帮助记忆其与狗相关的含义。';
    btn.addEventListener('click', () => {
        body.textContent = sample;
        backdrop.classList.add('active');
    });
    closeBtn?.addEventListener('click', () => backdrop.classList.remove('active'));
    backdrop?.addEventListener('click', (e) => {
        if (e.target === backdrop) backdrop.classList.remove('active');
    });
}

// 登录页 Tab 切换
function setupAuthTabs() {
    const tabs = document.querySelectorAll('[data-auth-tab]');
    const panes = document.querySelectorAll('[data-auth-pane]');
    if (!tabs.length) return;
    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            const target = tab.getAttribute('data-auth-tab');
            tabs.forEach(t => t.classList.toggle('active', t === tab));
            panes.forEach(p => p.style.display = p.getAttribute('data-auth-pane') === target ? 'block' : 'none');
        });
    });
}

// 初始化入口，按页面调用
document.addEventListener('DOMContentLoaded', () => {
    const active = document.body.getAttribute('data-active');
    injectTabbar(active);
    setupAIMnemonic();
    setupAuthTabs();

    // 图表演示：每日总结与报告页
    document.querySelectorAll('[data-ring]').forEach(el => {
        const v = parseInt(el.getAttribute('data-ring') || '72', 10);
        drawRingProgress(el, v);
    });
    const line = document.querySelector('#trend');
    if (line) {
        drawLineTrend(line, [120, 140, 135, 155, 170, 165, 190]);
    }
});


