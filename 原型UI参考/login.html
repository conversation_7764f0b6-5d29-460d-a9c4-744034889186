<!doctype html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>词汇汪 · 登录/注册</title>
    <link rel="stylesheet" href="styles.css" />
</head>

<body>
    <header class="topbar">
        <h1>登录/注册</h1>
    </header>

    <main class="container">
        <div class="hero-illust">
            <img src="images/词汇汪登录注册页面设计.png" alt="词汇汪 插画" style="width:220px; border-radius:16px;" />
        </div>

        <div class="tabs mt-16">
            <div class="tab active" data-auth-tab="phone">手机号</div>
            <div class="tab" data-auth-tab="email">邮箱</div>
        </div>

        <section class="card mt-16">
            <div data-auth-pane="phone">
                <div class="field mt-8">
                    <label class="muted">手机号</label>
                    <input class="input" placeholder="请输入手机号" />
                </div>
                <div class="field mt-12">
                    <label class="muted">验证码</label>
                    <div class="row">
                        <input class="input" style="flex:1" placeholder="输入验证码" />
                        <button class="btn btn-outline">获取验证码</button>
                    </div>
                </div>
                <button class="btn btn-primary btn-full mt-16" onclick="location.href='index.html'">登录 / 注册</button>
            </div>

            <div data-auth-pane="email" style="display:none;">
                <div class="field mt-8">
                    <label class="muted">邮箱</label>
                    <input class="input" placeholder="请输入邮箱" />
                </div>
                <div class="field mt-12">
                    <label class="muted">密码</label>
                    <input class="input" type="password" placeholder="请输入密码" />
                </div>
                <button class="btn btn-primary btn-full mt-16" onclick="location.href='index.html'">登录</button>
            </div>
        </section>

        <div class="muted center mt-12">登录即表示同意《服务条款》和《隐私政策》</div>
    </main>

    <script src="app.js"></script>
</body>

</html>