<!doctype html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>词汇汪 · 学习中心</title>
    <link rel="stylesheet" href="styles.css" />
</head>

<body data-active="home">
    <header class="topbar">
        <h1>词汇汪</h1>
        <div class="subtitle">极简·专注·高效</div>
    </header>

    <main class="container">
        <section class="hero card">
            <div class="space-between">
                <div>
                    <h2>早安，继续加油！</h2>
                    <div class="muted">今日目标：30 个新词 · 复习 40 个</div>
                </div>
                <img src="images/词汇汪IP形象设计方案4.png" alt="词汇汪"
                    style="width:72px;height:72px;border-radius:12px;object-fit:cover;" />
            </div>
            <button class="btn btn-cta btn-full mt-16" onclick="location.href='learning.html'">开始今日学习</button>
        </section>

        <section>
            <div class="grid-2">
                <a href="review.html" class="card mode-card" style="position:relative">
                    <div class="space-between">
                        <div>
                            <div style="font-weight:700;">复习任务</div>
                            <div class="muted mt-8">待复习 40</div>
                        </div>
                        <span class="badge">40</span>
                    </div>
                    <div class="progress mt-12"><span style="width: 60%"></span></div>
                </a>
                <a href="group-pk.html" class="card mode-card">
                    <div class="space-between">
                        <div>
                            <div style="font-weight:700;">小组PK</div>
                            <div class="muted mt-8">本周进行中</div>
                        </div>
                        <div style="font-size:20px;">🏆</div>
                    </div>
                    <div class="progress mt-12"><span style="width: 35%"></span></div>
                </a>
            </div>
        </section>
    </main>

    <nav id="tabbar" class="tabbar"></nav>

    <script src="app.js"></script>
</body>

</html>