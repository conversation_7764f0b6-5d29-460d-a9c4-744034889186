<!doctype html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>词汇汪 · 学习报告</title>
    <link rel="stylesheet" href="styles.css" />
</head>

<body data-active="report">
    <header class="topbar">
        <h1>学习报告</h1>
    </header>

    <main class="container">
        <section class="card">
            <div class="section-title">长期数据</div>
            <div class="grid-2">
                <div>
                    <div class="muted">总词汇量</div>
                    <div style="font-size:28px;font-weight:800;">3,450</div>
                </div>
                <div>
                    <div class="muted">连续打卡</div>
                    <div style="font-size:28px;font-weight:800;">12 天</div>
                </div>
            </div>
            <div class="chart-row mt-16">
                <div class="center">
                    <canvas data-ring="64"></canvas>
                    <div class="muted mt-8">本周目标完成度</div>
                </div>
                <div class="center">
                    <canvas id="trend"></canvas>
                    <div class="muted mt-8">艾宾浩斯遗忘曲线（示意）</div>
                </div>
            </div>
        </section>

        <section class="card mt-16">
            <div class="section-title">学习日历</div>
            <div class="grid-2" style="grid-template-columns: repeat(7, 1fr); gap:8px;">
                <div class="card" style="padding:10px;background:#fff;">S</div>
                <div class="card" style="padding:10px;background:#fff;">M</div>
                <div class="card" style="padding:10px;background:#fff;">T</div>
                <div class="card" style="padding:10px;background:#fff;">W</div>
                <div class="card" style="padding:10px;background:#fff;">T</div>
                <div class="card" style="padding:10px;background:#fff;">F</div>
                <div class="card" style="padding:10px;background:#fff;">S</div>
            </div>
            <div class="muted mt-12">绿色深浅代表当天学习强度</div>
        </section>
    </main>

    <nav id="tabbar" class="tabbar"></nav>
    <script src="app.js"></script>
</body>

</html>