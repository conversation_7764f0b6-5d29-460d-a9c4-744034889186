### **《词汇汪》AI背单词App - 功能需求文档 (PRD)**

**版本：V1.0**
**日期：2025年8月8日**

#### **1. 项目概述 (Project Overview)**

**1.1. 项目愿景**
《词汇汪》是一款旨在通过**智能算法、AI内容生成与轻社交激励**，为用户提供高效、个性化且充满乐趣的单词学习体验的移动应用。它不仅是学生的自学工具，也是教师的教学辅助平台。

**1.2. 核心理念**
*   **算法驱动效率：** 以改进型间隔重复算法为核心，实现科学记忆。
*   **AI丰富内容：** 利用LLM动态生成助记、短文和总结，提升学习深度。
*   **情感化伴学：** 以轻量化虚拟IP“词汇汪”提供情感支持与激励。
*   **教学生态闭环：** 连接学生与教师，通过任务和PK模式增强互动。

**1.3. 目标用户**
*   **学生端用户 (App)：** 需要系统性背诵单词的各类学生（小学生、初高学生、大学生、考研、留学等）。
*   **教师端用户 (Web)：** 希望布置、监督和管理学生单词学习任务的教师或培训机构。

---

#### **2. 技术栈与架构 (Tech Stack & Architecture)**

**2.1. 整体架构**
采用前后端分离架构，学生端为移动App，教师端为Web应用。

**2.2. 技术选型**
*   **学生端 (App)：** **Flutter** 框架，实现跨iOS/Android平台。
*   **教师端 (Web)：** **Vue**框架。
*   **后端：** **springboot**。
*   **数据库：** **MYSQL** (云端主数据库) + **SQLite/Hive** (App本地缓存)。
*   **AI 服务：**
    *   **大型语言模型 (LLM)：** 通过API调用 **Google Gemini** 或同类服务。
    *   **文本转语音 (TTS)：** 通过API调用云服务。ai接口和文本语音接口先预留，后续我再给你具体的调用文档。

---

#### **3. 核心算法详解**

**3.1. 记忆算法：改进型SM-2间隔重复系统 (SRS)**
此算法为App的大脑，用于动态计算每个单词的最佳复习时间。

*   **数据模型 (每个用户-单词对)：**
    *   `repetition_count (n)`: 整数，成功回忆次数。
    *   `ease_factor (EF)`: 浮点数，简易度因子，初始值2.5，最低1.3。
    *   `interval (I)`: 整数，下次复习间隔天数。
    *   `next_review_date`: 日期，下次复习的具体日期。
*   **用户反馈简化：**
    *   【忘记了】：映射为评分 `q < 3`。
    *   【模糊/有点熟】：映射为评分 `q = 4`。
    *   【记住了】：映射为评分 `q = 5`。
*   **算法流程：**
    1.  **若用户反馈为【忘记了】(q<3)：**
        *   `n` 重置为 0。
        *   `I` 重置为 1 (天)。
    2.  **若用户反馈为【模糊】或【记住了】(q>=4)：**
        *   更新 `EF`：`EF' = EF + [0.1 - (5 - q) * (0.08 + (5 - q) * 0.02)]` (确保`EF'` >= 1.3)。
        *   `n` = `n` + 1。
        *   更新 `I`：
            *   若 `n=1`，`I=1`。
            *   若 `n=2`，`I=6`。
            *   若 `n>2`，`I = I(n-1) * EF'`。
    3.  **设置下次复习日期：** `next_review_date` = `今日` + `新I值`。

**3.2. "复习雪崩"解决方案**
*   **每日复习上限 (Daily Review Cap)：** 用户可设置每日最大复习量，超出部分顺延。系统优先推送最“过期”的单词。
*   **负载均衡 (Fuzz Factor)：** 计算出的`I`值会增加一个小的随机扰动（如±10%），打散未来任务。
*   **假期模式 (Vacation Mode)：** 用户可开启，期间所有复习计划冻结。

---

#### **4. 功能需求详情 (Functional Requirements)**

**4.1. 学生端 (App)**

| 模块 | 功能点ID | 功能描述 | 优先级 |
| :--- | :--- | :--- | :--- |
| **用户与账户** | F-S01 | 支持手机号/邮箱注册和登录。 | 高 |
| | F-S02 | 用户可以设置昵称、头像和每日学习目标。 | 高 |
| | F-S03 | 提供“假期模式”开关。 | 中 |
| **学习流程** | F-S04 | **单词卡片学习：** 展示单词、音标、释义、例句。支持TTS发音。 | 高 |
| | F-S05 | **学习反馈：** 提供“忘记了”、“模糊”、“记住了”三个按钮，用于驱动核心记忆算法。 | 高 |
| | F-S06 | **AI助记：** 提供一个按钮，点击后通过弹窗展示由LLM API生成的助记内容。 | 高 |
| **复习系统** | F-S07 | **复习中心：** 聚合多种复习模式入口。 | 高 |
| | F-S08 | **经典复习：** 看词选义、听音选词、单词拼写。 | 高 |
| | F-S09 | **游戏化复习：** 实现“单词消消乐”功能。 | 中 |
| | F-S10 | **AI短文复习：** 将当日需复习的多个单词，由LLM API智能生成一篇短文供用户阅读。 | 高 |
| **激励与互动** | F-S11 | **虚拟IP“词汇汪”：** 在关键节点（答对/错、完成任务）播放动画。 | 中 |
| | F-S12 | **AI每日总结：** 每日学习结束后，由LLM API生成一段个性化的文字总结与激励。 | 高 |
| | F-S13 | **加入班级：** 学生可通过教师提供的班级码加入班级。 | 中 |
| | F-S14 | **小组PK：** 参与教师发起的PK任务，查看实时团队排名和个人贡献。 | 中 |
| **数据报告** | F-S15 | **学习报告：** 可视化图表展示词汇量、学习日历、正确率等数据。 | 高 |

**4.2. 教师端 (Web)**

| 模块 | 功能点ID | 功能描述 | 优先级 |
| :--- | :--- | :--- | :--- |
| **账户与班级** | F-T01 | 教师注册登录。 | 高 |
| | F-T02 | 创建班级，系统自动生成唯一班级码。 | 高 |
| | F-T03 | 查看和管理班级内的学生列表。 | 高 |
| **教学管理** | F-T04 | **发布任务：** 从词库中选择单词，向班级或个人发布背诵任务，并设定起止时间。 | 高 |
| | F-T05 | **创建小组PK：** 将学生分组（支持手动/随机），设定PK规则与时长，并发布任务。 | 中 |
| | F-T06 | **学情监控：** 查看班级整体学习报告和学生个人学习详情。 | 高 |
| | F-T07 | **资源导出：** 支持将指定单词列表导出为可打印的文档格式。 | 中 |

---

#### **5. 非功能性需求 (Non-Functional Requirements)**

*   **性能：** App冷启动时间应低于3秒，界面切换流畅。
*   **UI/UX：** 界面设计遵循现代、极简、干净的风格，交互直观，易于上手。
*   **可靠性：** 后端服务可用性需达到99.9%。
*   **安全性：** 用户密码需加密存储，API通信需使用HTTPS。
*   **可扩展性：** 架构设计应便于未来增加新的词库、新的游戏模式或对接更多AI服务。

---