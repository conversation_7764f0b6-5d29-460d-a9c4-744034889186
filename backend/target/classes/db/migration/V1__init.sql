CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    email VARCHAR(128) UNIQUE,
    phone VARCHAR(32) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    nick <PERSON><PERSON><PERSON><PERSON>(64),
    role VARCHAR(16) NOT NULL DEFAULT 'STUDENT',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS word (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    book VARCHAR(32) NOT NULL,
    level VARCHAR(16),
    word VARCHAR(128) NOT NULL,
    phonetic VARCHAR(128),
    meaning TEXT NOT NULL,
    extra JSON,
    enabled BOOLEAN DEFAULT TRUE
);
CREATE INDEX idx_word_book ON word(book);
CREATE INDEX idx_word_word ON word(word);

CREATE TABLE IF NOT EXISTS srs_record (
    user_id BIGINT NOT NULL,
    word_id BIGINT NOT NULL,
    n INT NOT NULL DEFAULT 0,
    ef DECIMAL(3,2) NOT NULL DEFAULT 2.50,
    interval_days INT NOT NULL DEFAULT 0,
    next_review_date DATE NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, word_id)
);
CREATE INDEX idx_srs_user_next ON srs_record(user_id, next_review_date);

CREATE TABLE IF NOT EXISTS study_feedback (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    word_id BIGINT NOT NULL,
    quality_q TINYINT NOT NULL,
    source VARCHAR(16) NOT NULL,
    ts TIMESTAMP NOT NULL
);
CREATE INDEX idx_fb_user_ts ON study_feedback(user_id, ts);


