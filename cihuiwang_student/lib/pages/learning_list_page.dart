import 'package:flutter/material.dart';
import '../services/word_parser.dart';
import '../services/prefs_service.dart';
import '../services/tts_service.dart';
import 'learning_study_page.dart';

class LearningListPage extends StatefulWidget {
  const LearningListPage({super.key});
  @override
  State<LearningListPage> createState() => _LearningListPageState();
}

class _LearningListPageState extends State<LearningListPage> {
  late Future<List<_RowWord>> _future;
  final Set<int> _known = {}; // 已会索引
  int _undoIndex = -1; // 最近隐藏索引

  @override
  void initState() {
    super.initState();
    _future = _load();
  }

  Future<List<_RowWord>> _load() async {
    final goal = await PrefsService.getDailyGoal();
    final book = await PrefsService.getSelectedBook() ?? 'cet4';
    final parsed = book == 'cet6'
        ? await WordParserService.parseCet6Mixed()
        : await WordParserService.parseCet4Edited();
    final take = parsed.take(goal).toList();
    return [for (final p in take) _RowWord(p.word, p.phonetic, p.meaning)];
  }

  void _undo() {
    if (_undoIndex >= 0) {
      _known.remove(_undoIndex);
      setState(() => _undoIndex = -1);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('今日待学'),
        actions: [
          if (_undoIndex >= 0)
            IconButton(onPressed: _undo, icon: const Icon(Icons.undo_rounded))
        ],
      ),
      body: FutureBuilder<List<_RowWord>>(
        future: _future,
        builder: (context, snap) {
          if (!snap.hasData) {
            return const Center(child: CircularProgressIndicator());
          }
          final list = snap.data!;
          return ListView.separated(
            padding: const EdgeInsets.all(16),
            itemCount: list.length,
            separatorBuilder: (_, __) => const SizedBox(height: 10),
            itemBuilder: (context, i) {
              if (_known.contains(i)) return const SizedBox.shrink();
              final w = list[i];
              return _WordRow(
                word: w.word,
                phonetic: w.phonetic,
                meaning: w.meaning,
                onPlay: () => TtsService.play(w.word),
                onToggleKnown: () {
                  setState(() {
                    _known.add(i);
                    _undoIndex = i;
                  });
                },
              );
            },
          );
        },
      ),
      bottomNavigationBar: SafeArea(
        minimum: const EdgeInsets.all(16),
        child: SizedBox(
          width: double.infinity,
          height: 52,
          child: ElevatedButton(
            onPressed: () async {
              final words = await _future;
              // 过滤掉已标记“已会”的行
              final filtered = <ParsedWord>[];
              for (var i = 0; i < words.length; i++) {
                if (_known.contains(i)) continue;
                final r = words[i];
                filtered.add(ParsedWord(word: r.word, phonetic: r.phonetic, meaning: r.meaning));
              }
              if (filtered.isEmpty) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('今日待学单词已全部标记为已会')),
                  );
                }
                return;
              }
              if (!context.mounted) return;
              await Navigator.of(context).push(
                MaterialPageRoute(builder: (_) => LearningStudyPage(preset: filtered)),
              );
            },
            child: const Text('进入学习'),
          ),
        ),
      ),
    );
  }
}

class _RowWord {
  final String word; final String? phonetic; final String meaning;
  _RowWord(this.word, this.phonetic, this.meaning);
}

class _WordRow extends StatefulWidget {
  final String word; final String? phonetic; final String meaning;
  final VoidCallback onPlay; final VoidCallback onToggleKnown;
  const _WordRow({required this.word, required this.phonetic, required this.meaning, required this.onPlay, required this.onToggleKnown});
  @override
  State<_WordRow> createState() => _WordRowState();
}

class _WordRowState extends State<_WordRow> {
  bool _show = false;

  @override
  Widget build(BuildContext context) {
    return AnimatedSize(
      duration: const Duration(milliseconds: 220),
      curve: Curves.easeInOut,
      child: Card(
        child: InkWell(
          onTap: () => setState(() => _show = !_show),
          child: Padding(
            padding: const EdgeInsets.all(14),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                    Row(children: [
                      Text(widget.word, style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w700)),
                      const SizedBox(width: 8),
                      if (widget.phonetic != null)
                        Text('[${widget.phonetic}]', style: const TextStyle(color: Color(0xFF6B7280))),
                      IconButton(onPressed: widget.onPlay, icon: const Icon(Icons.volume_up_outlined, size: 20)),
                    ]),
                    if (_show) ...[
                      const SizedBox(height: 6),
                      Text(widget.meaning, style: const TextStyle(color: Color(0xFF374151))),
                    ]
                  ]),
                ),
                const SizedBox(width: 12),
                _KnownButton(onTap: widget.onToggleKnown),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _KnownButton extends StatefulWidget {
  final VoidCallback onTap; const _KnownButton({required this.onTap});
  @override
  State<_KnownButton> createState() => _KnownButtonState();
}

class _KnownButtonState extends State<_KnownButton> {
  bool _isKnown = false;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        setState(() => _isKnown = true);
        widget.onTap();
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 180),
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
        decoration: BoxDecoration(
          color: _isKnown ? const Color(0xFF22C55E) : Colors.white,
          borderRadius: BorderRadius.circular(999),
          border: Border.all(color: _isKnown ? const Color(0xFF22C55E) : const Color(0xFFE5E7EB)),
        ),
        child: Text('已会', style: TextStyle(color: _isKnown ? Colors.white : const Color(0xFF111827), fontWeight: FontWeight.w700)),
      ),
    );
  }
}


