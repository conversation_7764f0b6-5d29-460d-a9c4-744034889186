import 'package:flutter/material.dart';
import '../services/local_store.dart';
import '../services/word_parser.dart';
import 'review_quiz_page.dart';

/// 错题本：从本地反馈中收集 q<5 的词，支持回刷
class WrongBookPage extends StatefulWidget {
  const WrongBookPage({super.key});
  @override
  State<WrongBookPage> createState() => _WrongBookPageState();
}

class _WrongBookPageState extends State<WrongBookPage> {
  late Future<List<String>> _future;

  @override
  void initState() {
    super.initState();
    _future = _load();
  }

  Future<List<String>> _load() async {
    final recs = LocalStore.getFeedback();
    final words = <String>{};
    for (final m in recs) {
      final q = m['q'] as int? ?? 5;
      final w = m['word'] as String?;
      if (w != null && q < 5) words.add(w);
    }
    return words.toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('错题本')),
      body: FutureBuilder<List<String>>(
        future: _future,
        builder: (context, snap) {
          if (!snap.hasData) return const Center(child: CircularProgressIndicator());
          final words = snap.data!;
          if (words.isEmpty) return const Center(child: Text('最近没有错题，继续保持！'));
          return Column(children: [
            Expanded(
              child: ListView.separated(
                padding: const EdgeInsets.all(16),
                itemBuilder: (_, i) => Text(words[i], style: const TextStyle(fontSize: 16)),
                separatorBuilder: (_, __) => const Divider(height: 12),
                itemCount: words.length,
              ),
            ),
            SafeArea(
              minimum: const EdgeInsets.all(16),
              child: SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  onPressed: () async {
                    // 简易聚合：从四级词表取释义，筛出错题词
                    final all = await WordParserService.parseCet4Edited();
                    final selected = all.where((p) => words.contains(p.word)).toList();
                    if (!context.mounted) return;
                    await Navigator.of(context).push(
                      MaterialPageRoute(builder: (_) => ReviewQuizPage(words: selected, mode: QuizMode.enToCn)),
                    );
                  },
                  child: const Text('开始回刷'),
                ),
              ),
            )
          ]);
        },
      ),
    );
  }
}




