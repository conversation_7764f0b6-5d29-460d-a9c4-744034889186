import 'package:flutter/material.dart';
import '../services/word_parser.dart';
import '../services/tts_service.dart';

class ReviewDictationPage extends StatefulWidget {
  final List<ParsedWord> words;
  const ReviewDictationPage({super.key, required this.words});
  @override
  State<ReviewDictationPage> createState() => _ReviewDictationPageState();
}

class _ReviewDictationPageState extends State<ReviewDictationPage> {
  int _idx = 0;
  final _controller = TextEditingController();
  String? _result;

  @override
  Widget build(BuildContext context) {
    final w = widget.words[_idx];
    return Scaffold(
      appBar: AppBar(title: const Text('听写复习')),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Text('释义：${w.meaning}', style: const TextStyle(color: Color(0xFF6B7280))),
          const SizedBox(height: 12),
          Row(children: [
            ElevatedButton.icon(onPressed: () => TtsService.play(w.word), icon: const Icon(Icons.volume_up_outlined), label: const Text('播放')), 
            const SizedBox(width: 8),
            OutlinedButton(onPressed: () => setState(() => _result = w.word), child: const Text('看答案')),
          ]),
          const SizedBox(height: 16),
          TextField(controller: _controller, decoration: const InputDecoration(hintText: '输入你听到的单词')), 
          const SizedBox(height: 12),
          if (_result != null) Text('答案：$_result'),
          const Spacer(),
          SizedBox(
            width: double.infinity,
            height: 48,
            child: ElevatedButton(
              onPressed: () {
                setState(() {
                  _controller.clear();
                  _result = null;
                  _idx = (_idx + 1).clamp(0, widget.words.length - 1);
                });
                if (_idx == widget.words.length - 1) Navigator.pop(context);
              },
              child: const Text('下一题'),
            ),
          ),
        ]),
      ),
    );
  }
}


