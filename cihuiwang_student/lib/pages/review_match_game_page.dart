import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/word_parser.dart';

class ReviewMatchGamePage extends StatefulWidget {
  final List<ParsedWord>? words; // 可传入指定词列表
  const ReviewMatchGamePage({super.key, this.words});
  @override
  State<ReviewMatchGamePage> createState() => _ReviewMatchGamePageState();
}

enum GameDifficulty { easy, medium, hard }

class _ReviewMatchGamePageState extends State<ReviewMatchGamePage> with TickerProviderStateMixin {
  final Random _rng = Random();
  late List<_Tile> _tiles; // 网格卡片
  _Tile? _first;
  _Tile? _second;
  int _moves = 0;
  Timer? _timer;
  int _seconds = 0;
  bool _resolving = false; // 防止点太快导致第三张被翻开
  final List<Color> _palette = const [
    Color(0xFFFFE082), Color(0xFFFFCCBC), Color(0xFFB3E5FC), Color(0xFFC8E6C9),
    Color(0xFFD1C4E9), Color(0xFFFFF9C4), Color(0xFFFFF3E0), Color(0xFFE1F5FE),
  ];

  // 关卡：1 简单、2 普通、3 困难
  int _level = 1;
  int _crossAxisCount = 3;
  late AnimationController _celeCtrl;
  late AnimationController _thumbCtrl;
  bool _showCelebration = false;
  List<_Particle> _particles = const [];
  bool _didEnterDone = false;
  int _roundWordCount = 0; // 本轮复习单词数（以配对为单位）
  int _totalSeconds = 0; // 三关累计用时
  int _totalWords = 0;   // 三关累计单词数

  @override
  void initState() {
    super.initState();
    _celeCtrl = AnimationController(vsync: this, duration: const Duration(milliseconds: 1500))
      ..addStatusListener((s) { if (s == AnimationStatus.completed) setState(() => _showCelebration = false); });
    _thumbCtrl = AnimationController(vsync: this, duration: const Duration(milliseconds: 1400));
    _buildBoard();
    _timer = Timer.periodic(const Duration(seconds: 1), (_) => setState(() => _seconds++));
  }

  @override
  void dispose() {
    _timer?.cancel();
    _celeCtrl.dispose();
    _thumbCtrl.dispose();
    super.dispose();
  }

  Future<void> _buildBoard() async {
    final source = widget.words ?? (await WordParserService.parseCet4Edited());
    // 过滤异常词条：仅保留看起来像英文单词的条目
    final regWord = RegExp(r"^[A-Za-z][A-Za-z\-']{0,29}");
    final filtered = source.where((p) {
      final w = p.word.trim();
      final m = p.meaning.trim();
      if (w.isEmpty || m.isEmpty) return false;
      if (!regWord.hasMatch(w)) return false;
      return true;
    }).toList();
    // 根据关卡选择对数/列数
    final pairsByDiff = switch (_level) { 1 => 6, 2 => 10, _ => 12 };
    _crossAxisCount = switch (_level) { 1 => 3, 2 => 4, _ => 5 };
    final take = min(pairsByDiff, filtered.length);
    final pairs = filtered.take(take).toList();
    final tiles = <_Tile>[];
    int pairId = 0;
    for (final p in pairs) {
      // 为每个卡片独立随机颜色，确保同对不同色，打散“颜色=配对”的提示
      final color1 = _palette[_rng.nextInt(_palette.length)];
      Color color2;
      do { color2 = _palette[_rng.nextInt(_palette.length)]; } while (color2 == color1 && _palette.length > 1);
      tiles.add(_Tile(id: tiles.length, pairId: pairId, text: p.word, isWord: true, color: color1));
      tiles.add(_Tile(id: tiles.length + 1, pairId: pairId, text: p.meaning, isWord: false, color: color2));
      pairId++;
    }
    tiles.shuffle(_rng);
    setState(() => _tiles = tiles);
    _moves = 0; _seconds = 0; _first = null; _second = null; _resolving = false; _roundWordCount = take;
  }

  void _onTap(_Tile t) {
    // 若正在结算或两张已选，禁止继续点
    if (_resolving) return;
    if (_first != null && _second != null) return;
    if (t.matched || t.revealed) return;
    setState(() => t.revealed = true);
    SystemSound.play(SystemSoundType.click);
    if (_first == null) {
      _first = t; return;
    }
    if (_second == null && t != _first) {
      _second = t; _moves++; _resolving = true;
      Future.delayed(const Duration(milliseconds: 400), (){
        if (!mounted) return;
        final first = _first; final second = _second;
        if (first == null || second == null) return;
        final ok = first.pairId == second.pairId && first.isWord != second.isWord;
        setState((){
          if (ok) {
            first.matched = true; second.matched = true;
            HapticFeedback.mediumImpact();
            // 碰撞抖动：先小幅弹跳
            first.bump = true; second.bump = true;
            // 120ms 后开始缩放淡出
            Future.delayed(const Duration(milliseconds: 120), (){
              if (!mounted) return;
              setState(() { first.bump = false; second.bump = false; first.removing = true; second.removing = true; });
              // 再过 250ms 真正移除并压缩网格
              Future.delayed(const Duration(milliseconds: 250), (){
                if (!mounted) return;
                setState(() {
                  first.removed = true; second.removed = true;
                  _tiles = _tiles.where((e) => !e.removed).toList();
                  _resolving = false;
                });
              });
            });
          } else {
            first.revealed = false; second.revealed = false;
            _resolving = false;
          }
          _first = null; _second = null;
        });
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final done = _tiles.isEmpty;
    if (done && !_didEnterDone) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (!mounted || _didEnterDone) return;
        _didEnterDone = true;
        _timer?.cancel();
        _thumbCtrl.forward(from: 0);
        SystemSound.play(SystemSoundType.alert);
        // 累加总用时与总单词数
        _totalSeconds += _seconds;
        _totalWords += _roundWordCount;
        if (_level == 3) _startCelebration();
      });
    }
    return Scaffold(
      appBar: AppBar(title: const Text('单词消消乐')),
      body: Stack(children: [
        Padding(
          padding: const EdgeInsets.all(16),
          child: Column(children: [
            Row(children: [
              Expanded(child: Text('步数：$_moves')),
              Expanded(
                child: Row(mainAxisAlignment: MainAxisAlignment.end, children: [
                  Text('用时：${_seconds}s'),
                  const SizedBox(width: 8),
                  TextButton(
                    onPressed: () {
                      setState(() { _level = 3; _tiles = []; });
                      _didEnterDone = false;
                    },
                    child: const Text('测试结算'),
                  ),
                ]),
              ),
            ]),
            const SizedBox(height: 8),
            Align(
              alignment: Alignment.centerLeft,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                decoration: BoxDecoration(color: const Color(0xFFEFF4FF), borderRadius: BorderRadius.circular(8)),
                child: Text('第$_level关 / 3', style: const TextStyle(fontWeight: FontWeight.w600)),
              ),
            ),
            const SizedBox(height: 12),
            if (!done)
              Expanded(
                child: GridView.builder(
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount: _crossAxisCount, mainAxisSpacing: 10, crossAxisSpacing: 10),
                  itemCount: _tiles.length,
                  itemBuilder: (_, i) => _TileWidget(tile: _tiles[i], onTap: _onTap),
                ),
              )
            else
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      ScaleTransition(
                        scale: CurvedAnimation(parent: _thumbCtrl, curve: Curves.elasticOut),
                        child: const Text('👍', style: TextStyle(fontSize: 80)),
                      ),
                      const SizedBox(height: 12),
                      if (_level < 3) ...[
                        Text('总用时：${ _seconds }s', style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
                        const SizedBox(height: 6),
                        Text('本次复习单词数：$_roundWordCount', style: const TextStyle(fontSize: 14)),
                      ] else ...[
                        Text('三关总用时：${ _totalSeconds }s', style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
                        const SizedBox(height: 6),
                        Text('总复习单词数：$_totalWords', style: const TextStyle(fontSize: 14)),
                      ],
                    ],
                  ),
                ),
              ),
            if (done)
              SafeArea(
                minimum: const EdgeInsets.only(top: 12),
                child: SizedBox(
                  width: double.infinity,
                  height: 48,
                  child: ElevatedButton(
                    onPressed: () {
                      if (_level < 3) {
                        setState(() { _level += 1; });
                        _buildBoard();
                        _didEnterDone = false; // 重置下次通关的进入逻辑
                        _timer?.cancel();
                        _timer = Timer.periodic(const Duration(seconds: 1), (_) => setState(() => _seconds++));
                      } else {
                        Navigator.of(context).pushNamedAndRemoveUntil('/home', (route) => false);
                      }
                    },
                    child: Text(_level < 3 ? '进入下一关' : '完成'),
                  ),
                ),
              ),
            if (done && _level == 3)
              SafeArea(
                minimum: const EdgeInsets.only(top: 8),
                child: SizedBox(
                  width: double.infinity,
                  height: 44,
                  child: TextButton(onPressed: () {
                    setState(() { _level = 1; _totalSeconds = 0; _totalWords = 0; _didEnterDone = false; });
                    _buildBoard();
                    _timer?.cancel();
                    _timer = Timer.periodic(const Duration(seconds: 1), (_) => setState(() => _seconds++));
                  }, child: const Text('再来一局')),
                ),
              ),
          ]),
        ),
        if (_showCelebration)
          Positioned.fill(
            child: IgnorePointer(
              child: AnimatedBuilder(
                animation: _celeCtrl,
                builder: (_, __) => CustomPaint(painter: _ConfettiPainter(_particles, _celeCtrl.value)),
              ),
            ),
          ),
      ]),
    );
  }

  void _startCelebration() {
    final size = MediaQuery.of(context).size;
    final originLeft = Offset(size.width * 0.1, size.height - 80);
    final originRight = Offset(size.width * 0.9, size.height - 80);
    final particles = <_Particle>[];
    for (int i = 0; i < 70; i++) {
      final fromLeft = i.isEven;
      final angDeg = fromLeft ? (-60 + _rng.nextInt(30)) : (-120 + _rng.nextInt(30));
      final angle = angDeg * pi / 180.0;
      final speed = 500 + _rng.nextInt(300);
      final color = Colors.primaries[_rng.nextInt(Colors.primaries.length)].shade300;
      particles.add(_Particle(
        origin: fromLeft ? originLeft : originRight,
        vx: cos(angle) * speed,
        vy: sin(angle) * speed,
        radius: 2.5 + _rng.nextDouble() * 3.5,
        color: color,
      ));
    }
    setState(() { _particles = particles; _showCelebration = true; });
    _celeCtrl.forward(from: 0);
  }
}

class _Tile {
  final int id; final int pairId; final String text; final bool isWord; final Color color;
  bool revealed = false; bool matched = false; bool removed = false; bool removing = false; bool bump = false;
  _Tile({required this.id, required this.pairId, required this.text, required this.isWord, required this.color});
}

class _TileWidget extends StatelessWidget {
  final _Tile tile; final void Function(_Tile) onTap;
  const _TileWidget({required this.tile, required this.onTap});
  @override
  Widget build(BuildContext context) {
    if (tile.removed) return const SizedBox.shrink();
    final faceColor = tile.matched ? const Color(0xFFD1FAE5) : Colors.white;
    return GestureDetector(
      onTap: () => onTap(tile),
      child: AnimatedOpacity(
        duration: const Duration(milliseconds: 220),
        opacity: tile.removing ? 0.0 : 1.0,
        child: AnimatedScale(
          duration: const Duration(milliseconds: 180),
          scale: tile.removing ? 0.7 : (tile.bump ? 1.08 : 1.0),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            decoration: BoxDecoration(
              color: tile.revealed || tile.matched ? faceColor : tile.color,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: const Color(0xFFE5E7EB)),
              boxShadow: tile.revealed || tile.matched
                  ? [const BoxShadow(color: Color(0x0F000000), blurRadius: 8, offset: Offset(0, 4))]
                  : null,
            ),
            alignment: Alignment.center,
            padding: const EdgeInsets.all(8),
            child: tile.revealed || tile.matched
                ? Text(tile.text, textAlign: TextAlign.center)
                : const Icon(Icons.question_mark, color: Colors.white),
          ),
        ),
      ),
    );
  }
}

// 轻量礼花粒子与绘制
class _Particle {
  final Offset origin; final double vx; final double vy; final double radius; final Color color;
  const _Particle({required this.origin, required this.vx, required this.vy, required this.radius, required this.color});
}

class _ConfettiPainter extends CustomPainter {
  final List<_Particle> particles; final double t; // 0..1
  _ConfettiPainter(this.particles, this.t);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;
    const double gAccel = 900.0; // 重力加速度
    for (final p in particles) {
      final time = t * 1.5;
      final dx = p.vx * time;
      final dy = p.vy * time + 0.5 * gAccel * time * time;
      final pos = Offset(p.origin.dx + dx, p.origin.dy + dy);
      final alpha = (1.0 - t).clamp(0.0, 1.0);
      // 避免 withOpacity 的弃用告警
      final c = p.color;
      final rr = ((c.r * 255.0).round() & 0xff);
      final gg = ((c.g * 255.0).round() & 0xff);
      final bb = ((c.b * 255.0).round() & 0xff);
      paint.color = Color.fromRGBO(rr, gg, bb, alpha);
      canvas.drawCircle(pos, p.radius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant _ConfettiPainter oldDelegate) => oldDelegate.t != t || oldDelegate.particles != particles;
}



