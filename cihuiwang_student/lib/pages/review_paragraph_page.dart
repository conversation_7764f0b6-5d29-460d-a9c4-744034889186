import 'package:flutter/material.dart';
import '../services/ai_service.dart';

class ReviewParagraphPage extends StatelessWidget {
  final List<String> words;
  const ReviewParagraphPage({super.key, required this.words});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('短文复习')),
      body: FutureBuilder<String>(
        future: AiService.paragraph(words),
        builder: (context, snap) {
          final text = snap.data ?? '生成短文中…';
          return Padding(
            padding: const EdgeInsets.all(16),
            child: Text(text, style: const TextStyle(fontSize: 16, height: 1.5)),
          );
        },
      ),
    );
  }
}


