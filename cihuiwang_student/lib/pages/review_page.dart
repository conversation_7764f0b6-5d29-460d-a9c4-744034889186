import 'package:flutter/material.dart';
import '../services/word_parser.dart';
import '../services/srs_store.dart';
import '../services/srs_service.dart';
import 'review_quiz_page.dart';
import 'review_dictation_page.dart';
import 'review_paragraph_page.dart';
import 'wrong_book_page.dart';
import 'review_match_game_page.dart';

class ReviewPage extends StatelessWidget {
  const ReviewPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('复习中心')),
      body: GridView.count(
        padding: const EdgeInsets.all(16),
        crossAxisCount: 2,
        crossAxisSpacing: 14,
        mainAxisSpacing: 14,
        children: [
          _ModeCard(
            icon: Icons.style,
            title: '经典复习',
            subtitle: '逐词回顾',
            color: const Color(0xFF5B8DEF),
            onTap: () async {
              // 根据 SRS 计算应复习集合；为测试，若为空则构造“昨天学习30个”的随机记录
              final dueKeys = SrsStore.dueWordKeys();
              List<ParsedWord> words;
              if (dueKeys.isEmpty) {
                final list = await WordParserService.parseCet4Edited();
                final sample = list.take(30).toList();
                final now = DateTime.now();
                for (final w in sample) {
                  final q = [0,4,5][DateTime.now().millisecond % 3];
                  final rec = SrsService.updateByQuality(SrsRecord(nextReviewDate: now.subtract(const Duration(days: 1))), q);
                  await SrsStore.upsertRecord(w.word, rec);
                }
                words = sample;
              } else {
                final list = await WordParserService.parseCet4Edited();
                final setKeys = dueKeys.toSet();
                words = list.where((w) => setKeys.contains(w.word)).take(50).toList();
              }
              if (!context.mounted) return;
              Navigator.of(context).push(MaterialPageRoute(builder: (_) => ReviewQuizFlow(words: words)));
            },
          ),
          _ModeCard(
            icon: Icons.extension,
            title: '单词消消乐',
            subtitle: '匹配词义',
            color: const Color(0xFFFFD166),
            onTap: () async {
              final list = await WordParserService.parseCet4Edited();
              if (!context.mounted) return;
              Navigator.of(context).push(
                MaterialPageRoute(builder: (_) => ReviewMatchGamePage(words: list.take(20).toList())),
              );
            },
          ),
          _ModeCard(
            icon: Icons.record_voice_over,
            title: '听写复习',
            subtitle: '听音拼写',
            color: const Color(0xFF22C55E),
            onTap: () async {
              // 临时从四级解析前 20 个词做演示
              final list = await WordParserService.parseCet4Edited();
              // ignore: use_build_context_synchronously
              Navigator.of(context).push(
                MaterialPageRoute(builder: (_) => ReviewDictationPage(words: list.take(20).toList())),
              );
            },
          ),
          _ModeCard(
            icon: Icons.article,
            title: '短文复习',
            subtitle: '整合当日词',
            color: const Color(0xFF8B5CF6),
            onTap: () async {
              final list = await WordParserService.parseCet4Edited();
              // ignore: use_build_context_synchronously
              Navigator.of(context).push(
                MaterialPageRoute(builder: (_) => ReviewParagraphPage(words: [for (final w in list.take(20)) w.word])),
              );
            },
          ),
          _ModeCard(
            icon: Icons.bookmark_added_outlined,
            title: '错题本',
            subtitle: '集中回刷',
            color: const Color(0xFF10B981),
            onTap: () => Navigator.of(context).push(MaterialPageRoute(builder: (_) => const WrongBookPage())),
          ),
        ],
      ),
    );
  }
}

class _ModeCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final Color color;
  final VoidCallback? onTap;
  const _ModeCard({required this.icon, required this.title, required this.subtitle, required this.color, this.onTap});
  @override
  Widget build(BuildContext context) {
    return InkWell(
      borderRadius: BorderRadius.circular(18),
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(18),
          boxShadow: const [
            BoxShadow(
              color: Color(0x1A000000),
              blurRadius: 16,
              offset: Offset(0, 8),
            )
          ],
        ),
        clipBehavior: Clip.antiAlias,
        child: Stack(
          children: [
            // 背景浅色插画：保留左上角渐变光斑，去除右下角大图标
            Positioned(
              left: -30,
              top: -30,
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [
                      color.withValues(alpha: 0.10),
                      color.withValues(alpha: 0.03),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
              ),
            ),
            // 前景内容
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 42,
                    height: 42,
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.12),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(icon, color: color),
                  ),
                  const SizedBox(height: 12),
                  Text(title, style: const TextStyle(fontWeight: FontWeight.w800, fontSize: 16)),
                  const SizedBox(height: 6),
                  Text(subtitle, style: const TextStyle(color: Color(0xFF6B7280))),
                  const Spacer(),
                  Align(
                    alignment: Alignment.bottomRight,
                    child: Container(
                      width: 30,
                      height: 30,
                      decoration: BoxDecoration(
                        color: const Color(0xFFF3F4F6),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(Icons.arrow_forward_rounded, size: 18, color: Color(0xFF6B7280)),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
