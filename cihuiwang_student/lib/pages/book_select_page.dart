import 'package:flutter/material.dart';
import '../services/prefs_service.dart';

class BookSelectPage extends StatefulWidget {
  const BookSelectPage({super.key});
  @override
  State<BookSelectPage> createState() => _BookSelectPageState();
}

class _BookSelectPageState extends State<BookSelectPage> {
  String? _selected;
  @override
  void initState() {
    super.initState();
    PrefsService.getSelectedBook().then((v) => setState(() => _selected = v));
  }

  Future<void> _choose(String id) async {
    try {
      await PrefsService.setSelectedBook(id);
      if (!mounted) return;
      setState(() => _selected = id);
      Navigator.pop(context, id);
    } catch (e) {
      // 如果存储失败，仍然更新 UI 并允许返回
      if (!mounted) return;
      setState(() => _selected = id);
      Navigator.pop(context, id);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('选择词书')),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _BookTile(
            title: '四级词书（含音标）',
            subtitle: 'CET-4 · 约 3000 词',
            selected: _selected == 'cet4',
            onTap: () => _choose('cet4'),
          ),
          const SizedBox(height: 12),
          _BookTile(
            title: '六级词书（无音标）',
            subtitle: 'CET-6 · 约 2500 词',
            selected: _selected == 'cet6',
            onTap: () => _choose('cet6'),
          ),
        ],
      ),
    );
  }
}

class _BookTile extends StatelessWidget {
  final String title; final String subtitle; final bool selected; final VoidCallback onTap;
  const _BookTile({required this.title, required this.subtitle, required this.selected, required this.onTap});
  @override
  Widget build(BuildContext context) {
    return InkWell(
      borderRadius: BorderRadius.circular(14),
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(14),
          boxShadow: const [BoxShadow(color: Color(0x14000000), blurRadius: 12, offset: Offset(0, 6))],
          border: Border.all(color: selected ? const Color(0xFF5B8DEF) : const Color(0xFFE5E7EB), width: 1),
        ),
        child: Row(
          children: [
            Icon(Icons.menu_book, color: selected ? const Color(0xFF5B8DEF) : const Color(0xFF6B7280)),
            const SizedBox(width: 12),
            Expanded(child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Text(title, style: const TextStyle(fontWeight: FontWeight.w700)),
              const SizedBox(height: 4),
              Text(subtitle, style: const TextStyle(color: Color(0xFF6B7280))),
            ])),
            if (selected) const Icon(Icons.check_circle, color: Color(0xFF5B8DEF))
          ],
        ),
      ),
    );
  }
}


