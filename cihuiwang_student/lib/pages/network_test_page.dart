import 'package:flutter/material.dart';
import '../services/word_api_service.dart';
import '../services/sync_service.dart';
import '../services/api_service.dart';

/// 网络测试页面
class NetworkTestPage extends StatefulWidget {
  const NetworkTestPage({super.key});

  @override
  State<NetworkTestPage> createState() => _NetworkTestPageState();
}

class _NetworkTestPageState extends State<NetworkTestPage> {
  bool _isLoading = false;
  Map<String, dynamic>? _testResults;
  List<WordDetailDto>? _sampleWords;
  BookStatsDto? _bookStats;
  
  @override
  void initState() {
    super.initState();
    _runInitialTest();
  }
  
  Future<void> _runInitialTest() async {
    await _testApiConnection();
  }
  
  /// 测试API连接
  Future<void> _testApiConnection() async {
    setState(() {
      _isLoading = true;
      _testResults = null;
    });
    
    try {
      // 测试基础连接
      final connectionResult = await WordApiService.testConnection();
      
      Map<String, dynamic> results = {
        'connection': connectionResult,
        'auth_status': ApiService.isAuthenticated,
      };
      
      // 如果连接成功，测试词汇API
      if (connectionResult['success'] == true) {
        try {
          final words = await WordApiService.getDailyWords(limit: 5);
          results['sample_words'] = words.length;
          _sampleWords = words;
        } catch (e) {
          results['sample_words_error'] = e.toString();
        }
        
        try {
          final stats = await WordApiService.getBookStats('cet4');
          results['book_stats'] = stats.toJson();
          _bookStats = stats;
        } catch (e) {
          results['book_stats_error'] = e.toString();
        }
      }
      
      setState(() {
        _testResults = results;
      });
    } catch (e) {
      setState(() {
        _testResults = {
          'error': e.toString(),
        };
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  /// 测试词汇搜索
  Future<void> _testWordSearch() async {
    try {
      final results = await WordApiService.searchWords('hello');
      if (mounted) {
        _showResultDialog('搜索结果', '找到 ${results.length} 个词汇');
      }
    } catch (e) {
      if (mounted) {
        _showResultDialog('搜索失败', e.toString());
      }
    }
  }
  
  /// 测试手动同步
  Future<void> _testManualSync() async {
    try {
      await SyncService.manualSync();
      if (mounted) {
        _showResultDialog('同步成功', '手动同步已完成');
      }
    } catch (e) {
      if (mounted) {
        _showResultDialog('同步失败', e.toString());
      }
    }
  }
  
  void _showResultDialog(String title, String content) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('网络功能测试'),
        actions: [
          IconButton(
            onPressed: _testApiConnection,
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView(
              padding: const EdgeInsets.all(16),
              children: [
                // 连接状态卡片
                _buildConnectionCard(),
                const SizedBox(height: 16),
                
                // 测试结果卡片
                if (_testResults != null) _buildTestResultsCard(),
                const SizedBox(height: 16),
                
                // 示例词汇卡片
                if (_sampleWords != null) _buildSampleWordsCard(),
                const SizedBox(height: 16),
                
                // 词书统计卡片
                if (_bookStats != null) _buildBookStatsCard(),
                const SizedBox(height: 16),
                
                // 操作按钮
                _buildActionButtons(),
              ],
            ),
    );
  }
  
  Widget _buildConnectionCard() {
    final isConnected = _testResults?['connection']?['success'] == true;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        children: [
          Row(
            children: [
              Icon(
                isConnected ? Icons.wifi : Icons.wifi_off,
                color: isConnected ? Colors.green : Colors.red,
              ),
              const SizedBox(width: 8),
              Text(
                isConnected ? '已连接到服务器' : '服务器连接失败',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          if (!isConnected && _testResults?['error'] != null) ...[
            const SizedBox(height: 8),
            Text(
              '错误: ${_testResults!['error']}',
              style: const TextStyle(color: Colors.red, fontSize: 12),
            ),
          ],
        ],
      ),
    );
  }
  
  Widget _buildTestResultsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '测试结果',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            const SizedBox(height: 8),
            Text(
              'API连接: ${_testResults!['connection']?['success'] == true ? '成功' : '失败'}',
            ),
            Text(
              '认证状态: ${_testResults!['auth_status'] ? '已认证' : '未认证'}',
            ),
            if (_testResults!.containsKey('sample_words'))
              Text('示例词汇: ${_testResults!['sample_words']} 个'),
            if (_testResults!.containsKey('sample_words_error'))
              Text(
                '词汇获取失败: ${_testResults!['sample_words_error']}',
                style: const TextStyle(color: Colors.red, fontSize: 12),
              ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildSampleWordsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '示例词汇',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            const SizedBox(height: 8),
            ...(_sampleWords!.take(3).map((word) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                children: [
                  Text(
                    word.word,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  if (word.phonetic != null) ...[
                    const SizedBox(width: 8),
                    Text(
                      '[${word.phonetic}]',
                      style: const TextStyle(color: Colors.blue),
                    ),
                  ],
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      word.meaning,
                      style: const TextStyle(fontSize: 12),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ))),
          ],
        ),
      ),
    );
  }
  
  Widget _buildBookStatsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'CET4词书统计',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
            const SizedBox(height: 8),
            Text('总词汇: ${_bookStats!.totalWords}'),
            Text('核心词汇: ${_bookStats!.coreWords}'),
            Text('常用词汇: ${_bookStats!.commonWords}'),
            Text('一般词汇: ${_bookStats!.generalWords}'),
          ],
        ),
      ),
    );
  }
  
  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _testWordSearch,
            child: const Text('测试词汇搜索'),
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _testManualSync,
            child: const Text('测试手动同步'),
          ),
        ),
        const SizedBox(height: 8),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _testApiConnection,
            child: const Text('重新测试连接'),
          ),
        ),
      ],
    );
  }
}
