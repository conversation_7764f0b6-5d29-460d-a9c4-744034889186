import 'dart:async';
import 'package:flutter/material.dart';
import '../services/word_parser.dart';
import '../services/prefs_service.dart';
import '../services/tts_service.dart';
import '../services/ai_service.dart';
import 'review_quiz_page.dart';
import 'study_result_page.dart';
import '../services/local_store.dart';

class LearningStudyPage extends StatefulWidget {
  final List<ParsedWord>? preset;
  const LearningStudyPage({super.key, this.preset});
  @override
  State<LearningStudyPage> createState() => _LearningStudyPageState();
}

class _LearningStudyPageState extends State<LearningStudyPage> {
  List<ParsedWord> _words = [];
  int _idx = 0;
  final List<int> _unknownIdx = []; // 总体不会的索引集合
  int _sinceLastQuizUnknown = 0;
  late DateTime _startTime;
  Timer? _timer;
  final List<ParsedWord> _wrongCollected = [];

  @override
  void initState() {
    super.initState();
    _load();
  }

  Future<void> _load() async {
    _startTime = DateTime.now();
    if (widget.preset != null) {
      _words = widget.preset!;
    } else {
      final goal = await PrefsService.getDailyGoal();
      final book = await PrefsService.getSelectedBook() ?? 'cet4';
      final parsed = book == 'cet6'
          ? await WordParserService.parseCet6Mixed()
          : await WordParserService.parseCet4Edited();
      _words = parsed.take(goal).toList();
    }
    setState(() {});
    _timer = Timer.periodic(const Duration(minutes: 10), (_) => _triggerFirstQuiz(force: true));
    // 维护 TTS 缓存（异步）
    // ignore: unawaited_futures
    TtsService.maintainCache();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  Future<void> _triggerFirstQuiz({bool force = false}) async {
    if (!mounted) return;
    if (_sinceLastQuizUnknown == 0 && !force) return;
    // 取最近一段的不认识词
    final slice = _unknownIdx.reversed.take(_sinceLastQuizUnknown).toList().reversed.map((i) => _words[i]).toList();
    _sinceLastQuizUnknown = 0;
    if (slice.isEmpty) return;
    if (!mounted) return;
    final wrong = await Navigator.of(context).push<List<ParsedWord>>(
      MaterialPageRoute(builder: (_) => ReviewQuizPage(words: slice, mode: QuizMode.enToCn)),
    );
    if (wrong != null && wrong.isNotEmpty) {
      _wrongCollected.addAll(wrong);
    }
  }

  Future<void> _finishAll() async {
    // 第二轮：对所有不会的词进行中文→英文
    // 若累计条数尚未触发第一轮，统一在此触发（剩余切片）
    if (_sinceLastQuizUnknown > 0) {
      final slice = _unknownIdx.reversed.take(_sinceLastQuizUnknown).toList().reversed.map((i) => _words[i]).toList();
      _sinceLastQuizUnknown = 0;
      if (slice.isNotEmpty) {
        if (!mounted) return;
        final wrong = await Navigator.of(context).push<List<ParsedWord>>(
          MaterialPageRoute(builder: (_) => ReviewQuizPage(words: slice, mode: QuizMode.enToCn)),
        );
        if (wrong != null && wrong.isNotEmpty) {
          _wrongCollected.addAll(wrong);
        }
      }
    }
    // 若从未做过第一轮（例如仅3个词），对全部词做一轮
    if (_wrongCollected.isEmpty) {
      if (!mounted) return;
      final wrongAll = await Navigator.of(context).push<List<ParsedWord>>(
        MaterialPageRoute(builder: (_) => ReviewQuizPage(words: _words, mode: QuizMode.enToCn)),
      );
      if (wrongAll != null) _wrongCollected.addAll(wrongAll);
    }
    final unknown = List<ParsedWord>.from(_wrongCollected);
    List<ParsedWord> wrong2 = const [];
    if (unknown.isNotEmpty) {
      if (!mounted) return;
      final res = await Navigator.of(context).push<List<ParsedWord>>(
        MaterialPageRoute(builder: (_) => ReviewQuizPage(words: unknown, mode: QuizMode.cnToEn)),
      );
      wrong2 = res ?? const [];
    }
    final duration = DateTime.now().difference(_startTime);
    if (!mounted) return;
    await Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (_) => StudyResultPage(
          total: _words.length,
          learned: _words.length - wrong2.length,
          duration: duration,
          wrongWords: [for (final w in wrong2) w.word],
          sessionStart: _startTime,
        ),
      ),
    );
  }

  void _mark(int q) async {
    // q=5 记住了；q=4 模糊；q=0 忘记
    if (q < 5) {
      _unknownIdx.add(_idx);
      _sinceLastQuizUnknown += 1;
      // 到达阈值触发第一轮复习（从8改为5）
      if (_sinceLastQuizUnknown >= 5) {
        await _triggerFirstQuiz();
      }
    }
    final next = _idx + 1;
    if (next >= _words.length) {
      await _finishAll();
      return;
    }
    setState(() {
      _idx = next;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_words.isEmpty) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }
    final w = _words[_idx];
    return Scaffold(
      appBar: AppBar(title: Text('${_idx + 1}/${_words.length} 学习')),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(children: [
          // 卡片内容使用 Expanded 占据可用空间，内部滚动，避免仅挤在上半部分
          Expanded(
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: SingleChildScrollView(
                  child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                    Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                      Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                        Text(w.word, style: const TextStyle(fontSize: 34, fontWeight: FontWeight.w800)),
                        const SizedBox(height: 6),
                        if (w.phonetic != null)
                          Text('[${w.phonetic}]', style: const TextStyle(color: Color(0xFF6B7280), fontSize: 16)),
                      ]),
                      OutlinedButton.icon(onPressed: () => TtsService.play(w.word), icon: const Icon(Icons.volume_up_outlined, size: 18), label: const Text('发音')),
                    ]),
                    const SizedBox(height: 16),
                    Text(w.meaning, style: const TextStyle(fontSize: 18, height: 1.5)),
                    const SizedBox(height: 16),
                    // 例句方块
                    _SectionCard(
                      title: '例句',
                      child: FutureBuilder<String>(
                        future: AiService.exampleSentence(w.word),
                        builder: (context, snap) {
                          final sentence = snap.data ?? 'Loading example…';
                          return Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(child: Text(sentence, style: const TextStyle(color: Color(0xFF374151), height: 1.55))),
                              const SizedBox(width: 8),
                              IconButton(onPressed: () => TtsService.playSentence(sentence), icon: const Icon(Icons.volume_up_outlined, size: 18)),
                            ],
                          );
                        },
                      ),
                    ),
                    const SizedBox(height: 12),
                    // AI 助记方块
                    _SectionCard(
                      title: 'AI 助记',
                      child: FutureBuilder<String>(
                        future: AiService.generateMnemonic(w.word),
                        builder: (context, snap) => Text(snap.data ?? 'AI助记生成中…', style: const TextStyle(color: Color(0xFF6B7280), height: 1.55)),
                      ),
                    ),
                    const SizedBox(height: 12),
                    // 笔记区域（按单词独立保存，使用 Key 触发重建）
                    KeyedSubtree(key: ValueKey(w.word), child: _NoteEditor(word: w.word)),
                  ]),
                ),
              ),
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: _actionBtn('下一条', const Color(0xFF5B8DEF), () => _mark(5)),
          ),
        ]),
      ),
    );
  }

  Widget _actionBtn(String text, Color color, VoidCallback onTap, {Color? fg}) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 56,
        decoration: BoxDecoration(color: color, borderRadius: BorderRadius.circular(999)),
        alignment: Alignment.center,
        child: Text(text, style: TextStyle(color: fg ?? Colors.white, fontWeight: FontWeight.w700)),
      ),
    );
  }
}


class _SectionCard extends StatelessWidget {
  final String title; final Widget child;
  const _SectionCard({required this.title, required this.child});
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE5E7EB)),
      ),
      padding: const EdgeInsets.all(12),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Text(title, style: const TextStyle(fontWeight: FontWeight.w700)),
        const SizedBox(height: 8),
        child,
      ]),
    );
  }
}

class _NoteEditor extends StatefulWidget {
  final String word; const _NoteEditor({required this.word});
  @override
  State<_NoteEditor> createState() => _NoteEditorState();
}

class _NoteEditorState extends State<_NoteEditor> {
  final _controller = TextEditingController();
  bool _loading = true;
  @override
  void initState() {
    super.initState();
    _controller.text = LocalStore.getNote(widget.word);
    _loading = false;
  }
  @override
  void didUpdateWidget(covariant _NoteEditor oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.word != widget.word) {
      _controller.text = LocalStore.getNote(widget.word);
    }
  }
  @override
  Widget build(BuildContext context) {
    if (_loading) return const SizedBox.shrink();
    return _SectionCard(
      title: '我的笔记',
      child: Padding(
        padding: const EdgeInsets.all(6),
        child: TextField(
          controller: _controller,
          maxLines: 4,
          decoration: const InputDecoration(
            isCollapsed: true,
            hintText: '记录你的要点/联想…',
            border: InputBorder.none,
            enabledBorder: InputBorder.none,
            focusedBorder: InputBorder.none,
            disabledBorder: InputBorder.none,
          ),
          onSubmitted: (v) => LocalStore.saveNote(widget.word, v),
          onChanged: (v) => LocalStore.saveNote(widget.word, v),
        ),
      ),
    );
  }
}


