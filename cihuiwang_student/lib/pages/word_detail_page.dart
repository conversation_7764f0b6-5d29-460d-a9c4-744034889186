import 'package:flutter/material.dart';
import '../services/word_api_service.dart';
import '../services/tts_service.dart';

/// 单词详情页面
class WordDetailPage extends StatefulWidget {
  final String word;
  
  const WordDetailPage({super.key, required this.word});

  @override
  State<WordDetailPage> createState() => _WordDetailPageState();
}

class _WordDetailPageState extends State<WordDetailPage> {
  WordDetailDto? _wordDetail;
  bool _isLoading = true;
  String? _error;
  
  @override
  void initState() {
    super.initState();
    _loadWordDetail();
  }
  
  Future<void> _loadWordDetail() async {
    try {
      final detail = await WordApiService.getWordDetail(widget.word);
      setState(() {
        _wordDetail = detail;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }
  
  /// 播放发音
  void _playPronunciation() {
    if (_wordDetail?.word != null) {
      TtsService.speak(_wordDetail!.word);
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_wordDetail?.word ?? widget.word),
        actions: [
          if (_wordDetail != null)
            IconButton(
              icon: const Icon(Icons.volume_up),
              onPressed: _playPronunciation,
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        '加载失败',
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _error!,
                        style: TextStyle(color: Colors.grey[500]),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadWordDetail,
                        child: const Text('重试'),
                      ),
                    ],
                  ),
                )
              : _wordDetail == null
                  ? const Center(child: Text('单词未找到'))
                  : SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 单词标题卡片
                          _buildWordCard(),
                          const SizedBox(height: 16),
                          
                          // 释义卡片
                          _buildMeaningCard(),
                          const SizedBox(height: 16),
                          
                          // 标签卡片
                          _buildTagsCard(),
                          const SizedBox(height: 16),
                          
                          // 词汇变换卡片
                          if (_wordDetail!.exchange != null && _wordDetail!.exchange!.isNotEmpty)
                            _buildExchangeCard(),
                          const SizedBox(height: 16),
                          
                          // 详细信息卡片
                          if (_wordDetail!.definition != null || _wordDetail!.pos != null)
                            _buildDetailCard(),
                        ],
                      ),
                    ),
    );
  }
  
  /// 构建单词卡片
  Widget _buildWordCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    _wordDetail!.word,
                    style: const TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: _playPronunciation,
                  icon: const Icon(Icons.volume_up, size: 28),
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.blue.withOpacity(0.1),
                    foregroundColor: Colors.blue,
                  ),
                ),
              ],
            ),
            if (_wordDetail!.phonetic != null) ...[
              const SizedBox(height: 8),
              Text(
                '[${_wordDetail!.phonetic}]',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.blue[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  /// 构建释义卡片
  Widget _buildMeaningCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.translate, color: Colors.green),
                SizedBox(width: 8),
                Text(
                  '中文释义',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              _wordDetail!.meaning,
              style: const TextStyle(
                fontSize: 16,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建标签卡片
  Widget _buildTagsCard() {
    final tags = _extractTags();
    if (tags.isEmpty) return const SizedBox.shrink();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.local_offer, color: Colors.orange),
                SizedBox(width: 8),
                Text(
                  '词汇标签',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: tags.map((tag) => _buildTag(tag)).toList(),
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建词汇变换卡片
  Widget _buildExchangeCard() {
    final exchanges = _parseExchange(_wordDetail!.exchange!);
    if (exchanges.isEmpty) return const SizedBox.shrink();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.swap_horiz, color: Colors.purple),
                SizedBox(width: 8),
                Text(
                  '词汇变换',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...exchanges.map((exchange) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 80,
                    child: Text(
                      '${exchange['type']}:',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      exchange['word'],
                      style: const TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }
  
  /// 构建详细信息卡片
  Widget _buildDetailCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue),
                SizedBox(width: 8),
                Text(
                  '详细信息',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (_wordDetail!.pos != null) ...[
              _buildDetailRow('词性', _wordDetail!.pos!),
              const SizedBox(height: 8),
            ],
            if (_wordDetail!.definition != null) ...[
              _buildDetailRow('英文释义', _wordDetail!.definition!),
              const SizedBox(height: 8),
            ],
            if (_wordDetail!.collins != null) ...[
              _buildDetailRow('柯林斯星级', '${_wordDetail!.collins} 星'),
              const SizedBox(height: 8),
            ],
            if (_wordDetail!.frq != null) ...[
              _buildDetailRow('词频排序', '#${_wordDetail!.frq}'),
            ],
          ],
        ),
      ),
    );
  }
  
  /// 构建详细信息行
  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 80,
          child: Text(
            '$label:',
            style: TextStyle(
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(fontSize: 15),
          ),
        ),
      ],
    );
  }
  
  /// 构建标签
  Widget _buildTag(Map<String, dynamic> tag) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: tag['color'],
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        tag['text'],
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
  
  /// 提取标签
  List<Map<String, dynamic>> _extractTags() {
    List<Map<String, dynamic>> tags = [];
    
    // 级别标签
    if (_wordDetail!.level != null) {
      tags.add(_getLevelTag(_wordDetail!.level!));
    }
    
    // 牛津核心词汇
    if (_wordDetail!.oxford == true) {
      tags.add({
        'text': '牛津核心',
        'color': Colors.amber[700],
      });
    }
    
    // 柯林斯星级
    if (_wordDetail!.collins != null && _wordDetail!.collins! > 0) {
      tags.add({
        'text': '柯林斯${_wordDetail!.collins}星',
        'color': Colors.red[400],
      });
    }
    
    // 从tag字段提取考试标签
    if (_wordDetail!.tag != null) {
      final examTags = _extractExamTags(_wordDetail!.tag!);
      tags.addAll(examTags);
    }
    
    return tags;
  }
  
  /// 获取级别标签
  Map<String, dynamic> _getLevelTag(String level) {
    switch (level.toLowerCase()) {
      case 'core':
        return {'text': '核心词汇', 'color': Colors.red[600]};
      case 'cet4':
        return {'text': '四级词汇', 'color': Colors.blue[600]};
      case 'cet6':
        return {'text': '六级词汇', 'color': Colors.purple[600]};
      case 'toefl':
        return {'text': '托福词汇', 'color': Colors.orange[600]};
      case 'ielts':
        return {'text': '雅思词汇', 'color': Colors.green[600]};
      case 'gre':
        return {'text': 'GRE词汇', 'color': Colors.indigo[600]};
      case 'junior':
        return {'text': '中考词汇', 'color': Colors.lightBlue[600]};
      case 'senior':
        return {'text': '高考词汇', 'color': Colors.deepPurple[600]};
      default:
        return {'text': level.toUpperCase(), 'color': Colors.grey[600]};
    }
  }
  
  /// 提取考试标签
  List<Map<String, dynamic>> _extractExamTags(String tagString) {
    List<Map<String, dynamic>> tags = [];
    
    if (tagString.contains('zk')) {
      tags.add({'text': '中考', 'color': Colors.lightBlue[500]});
    }
    if (tagString.contains('gk')) {
      tags.add({'text': '高考', 'color': Colors.deepPurple[500]});
    }
    if (tagString.contains('cet4')) {
      tags.add({'text': '四级', 'color': Colors.blue[500]});
    }
    if (tagString.contains('cet6')) {
      tags.add({'text': '六级', 'color': Colors.purple[500]});
    }
    if (tagString.contains('toefl')) {
      tags.add({'text': '托福', 'color': Colors.orange[500]});
    }
    if (tagString.contains('ielts')) {
      tags.add({'text': '雅思', 'color': Colors.green[500]});
    }
    if (tagString.contains('gre')) {
      tags.add({'text': 'GRE', 'color': Colors.indigo[500]});
    }
    
    return tags;
  }
  
  /// 解析词汇变换
  List<Map<String, String>> _parseExchange(String exchange) {
    List<Map<String, String>> result = [];
    
    final parts = exchange.split('/');
    for (final part in parts) {
      if (part.contains(':')) {
        final colonIndex = part.indexOf(':');
        final type = part.substring(0, colonIndex);
        final word = part.substring(colonIndex + 1);
        
        final typeText = _getExchangeTypeText(type);
        if (typeText.isNotEmpty) {
          result.add({
            'type': typeText,
            'word': word,
          });
        }
      }
    }
    
    return result;
  }
  
  /// 获取词汇变换类型文字
  String _getExchangeTypeText(String type) {
    switch (type) {
      case 'p':
        return '过去式';
      case 'd':
        return '过去分词';
      case 'i':
        return '现在分词';
      case '3':
        return '第三人称单数';
      case 'r':
        return '比较级';
      case 't':
        return '最高级';
      case 's':
        return '复数形式';
      case '0':
        return '原型';
      default:
        return '';
    }
  }
}
