import 'package:flutter/material.dart';
import 'book_select_page.dart';
import 'daily_goal_page.dart';
import '../services/prefs_service.dart';
import '../services/word_parser.dart';
import '../services/srs_store.dart';
// ignore_for_file: unused_import
import '../services/srs_service.dart';
import '../services/local_store.dart';
import 'review_quiz_page.dart';
import 'network_test_page.dart';

class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('我的')),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(14),
                    child: Image.asset(
                      'assets/images/词汇汪IP形象设计方案4.png',
                      width: 56,
                      height: 56,
                      fit: BoxFit.cover,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: const [
                      Text(
                        '词汇汪同学',
                        style: TextStyle(fontWeight: FontWeight.w700),
                      ),
                      SizedBox(height: 4),
                      Text(
                        '账户：<EMAIL>',
                        style: TextStyle(color: Color(0xFF6B7280)),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          _ItemTile(
            title: '账户信息',
            trailing: const Text(
              '更改',
              style: TextStyle(color: Color(0xFF6B7280)),
            ),
          ),
          _ItemTile(
            title: '选择词书',
            trailing: TextButton(
              onPressed: () => Navigator.of(context).push(
                MaterialPageRoute(builder: (_) => const BookSelectPage()),
              ),
              child: const Text('去选择'),
            ),
          ),
          _ItemTile(
            title: '每日学习目标设置',
            trailing: TextButton(
              onPressed: () => Navigator.of(context).push(
                MaterialPageRoute(builder: (_) => const DailyGoalPage()),
              ),
              child: const Text('设置'),
            ),
          ),
          _ItemTile(
            title: '发音偏好',
            trailing: Row(mainAxisSize: MainAxisSize.min, children: [
              TextButton(onPressed: () => PrefsService.setVoiceType(0), child: const Text('美音')),
              const SizedBox(width: 6),
              TextButton(onPressed: () => PrefsService.setVoiceType(1), child: const Text('英音')),
            ]),
          ),
          _ItemTile(
            title: '假期模式开关',
            trailing: Switch(value: false, onChanged: (v) {}),
          ),
          _ItemTile(
            title: '退出登录',
            trailing: TextButton(
              onPressed: () =>
                  Navigator.of(context).pushReplacementNamed('/login'),
              child: const Text('退出'),
            ),
          ),
          _ItemTile(
            title: '网络功能测试',
            trailing: TextButton(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(builder: (_) => const NetworkTestPage()),
                );
              },
              child: const Text('测试'),
            ),
          ),
          _ItemTile(
            title: '本地调试',
            trailing: TextButton(
              onPressed: () async {
                // 构造测试词与 SRS 状态，进入一个简单调试页
                final list = await WordParserService.parseCet4Edited();
                // ignore: use_build_context_synchronously
                Navigator.of(context).push(MaterialPageRoute(builder: (_) => _DebugPage(words: list.take(20).toList())));
              },
              child: const Text('进入'),
            ),
          ),
        ],
      ),
    );
  }
}

class _ItemTile extends StatelessWidget {
  final String title;
  final Widget trailing;
  const _ItemTile({required this.title, required this.trailing});
  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [Text(title), trailing],
        ),
      ),
    );
  }
}

class _DebugPage extends StatelessWidget {
  final List<ParsedWord> words;
  const _DebugPage({required this.words});
  @override
  Widget build(BuildContext context) {
    final all = SrsStore.all();
    return Scaffold(
      appBar: AppBar(title: const Text('调试信息')),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          SizedBox(
            width: double.infinity,
            height: 44,
            child: ElevatedButton(
              onPressed: () => Navigator.of(context).push(MaterialPageRoute(builder: (_) => ReviewQuizFlow(words: words))),
              child: const Text('直达经典复习流程'),
            ),
          ),
          const SizedBox(height: 12),
          Text('词数: ${words.length}'),
          const SizedBox(height: 8),
          const Text('SRS 记录（部分）：'),
          const SizedBox(height: 6),
          for (final entry in all.entries.take(50))
            Text('${entry.key} | n=${entry.value.repetitionCount} ef=${entry.value.easeFactor.toStringAsFixed(2)} i=${entry.value.intervalDays} next=${entry.value.nextReviewDate.toIso8601String()}'),
          const SizedBox(height: 12),
          const Text('最近反馈：'),
          for (final m in LocalStore.getFeedback().take(50))
            Text('${m['ts']} | ${m['word']} | q=${m['q']}'),
        ],
      ),
    );
  }
}

