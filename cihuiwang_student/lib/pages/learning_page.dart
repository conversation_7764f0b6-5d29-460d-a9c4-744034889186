import 'package:flutter/material.dart';
import '../repositories/word_repository.dart';
import '../services/ai_service.dart';
import '../pages/daily_summary_page.dart';

class LearningPage extends StatefulWidget {
  const LearningPage({super.key});

  @override
  State<LearningPage> createState() => _LearningPageState();
}

class _LearningPageState extends State<LearningPage> {
  bool _showMnemonic = false;
  String? _mnemonic;
  final _repo = WordRepository();
  late Future<Map<String, String>> _wordFuture;

  @override
  void initState() {
    super.initState();
    _wordFuture = () async {
      final list = await _repo.fetchTodayNewWords();
      final w = list.first;
      return {
        'word': w.word,
        'phonetic': w.phonetic,
        'meaning': w.meaning,
        'example': w.example,
      };
    }();
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('单词学习')),
      body: FutureBuilder<Map<String, String>>(
        future: _wordFuture,
        builder: (context, snap) {
          if (!snap.hasData) {
            return const Center(child: CircularProgressIndicator());
          }
          final w = snap.data!;
          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                w['word']!,
                                style: const TextStyle(
                                  fontSize: 28,
                                  fontWeight: FontWeight.w800,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  Text(
                                    w['phonetic']!,
                                    style: const TextStyle(color: Color(0xFF6B7280)),
                                  ),
                                  const SizedBox(width: 8),
                                  OutlinedButton.icon(
                                    onPressed: () {},
                                    icon: const Icon(Icons.volume_up_outlined, size: 18),
                                    label: const Text('发音'),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          OutlinedButton(
                            onPressed: () async {
                              final tip = await AiService.generateMnemonic(w['word']!);
                              setState(() {
                                _mnemonic = tip;
                                _showMnemonic = true;
                              });
                            },
                            child: const Text('AI助记'),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Text(w['meaning']!),
                      const SizedBox(height: 10),
                      Text(w['example']!, style: const TextStyle(color: Color(0xFF6B7280))),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(child: _StatusButton(text: '忘记了', color: const Color(0xFFEF4444), onTap: () {})),
                          const SizedBox(width: 12),
                          Expanded(child: _StatusButton(text: '模糊', color: const Color(0xFFF59E0B), fg: const Color(0xFF111827), onTap: () {})),
                          const SizedBox(width: 12),
                          Expanded(
                            child: _StatusButton(
                              text: '记住了',
                              color: const Color(0xFF22C55E),
                              onTap: () {
                                Navigator.of(context).push(MaterialPageRoute(builder: (_) => const DailySummaryPage()));
                              },
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
      bottomSheet: _showMnemonic
          ? _MnemonicSheet(
              content: _mnemonic ?? 'AI 助记加载中...',
              onClose: () => setState(() => _showMnemonic = false),
            )
          : null,
    );
  }
}

class _StatusButton extends StatelessWidget {
  final String text;
  final Color color;
  final Color? fg;
  final VoidCallback onTap;
  const _StatusButton({
    required this.text,
    required this.color,
    required this.onTap,
    this.fg,
  });
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 56,
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(999),
        ),
        alignment: Alignment.center,
        child: Text(
          text,
          style: TextStyle(
            color: fg ?? Colors.white,
            fontWeight: FontWeight.w700,
          ),
        ),
      ),
    );
  }
}

class _MnemonicSheet extends StatelessWidget {
  final String content;
  final VoidCallback onClose;
  const _MnemonicSheet({required this.content, required this.onClose});
  @override
  Widget build(BuildContext context) {
    return Material(
      elevation: 16,
      borderRadius: const BorderRadius.only(
        topLeft: Radius.circular(16),
        topRight: Radius.circular(16),
      ),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.fromLTRB(16, 12, 16, 16 + 8),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'AI助记',
                  style: TextStyle(fontWeight: FontWeight.w700),
                ),
                OutlinedButton(onPressed: onClose, child: const Text('关闭')),
              ],
            ),
            const SizedBox(height: 10),
            Text(content),
          ],
        ),
      ),
    );
  }
}
