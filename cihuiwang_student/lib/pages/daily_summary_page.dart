import 'package:flutter/material.dart';

class DailySummaryPage extends StatelessWidget {
  const DailySummaryPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('每日总结')),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          Column(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: Image.asset(
                  'assets/images/词汇汪IP形象设计方案4.png',
                  width: 120,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                '做得好！继续保持！',
                style: TextStyle(color: Color(0xFF6B7280)),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'AI 个性化总结',
                    style: TextStyle(fontWeight: FontWeight.w700),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '今天你掌握了 28 个新词，其中 20 个一次记住。建议明天优先复习：canine, aggregate, benevolent...',
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: const [
                      Expanded(child: _ChartPlaceholder(title: '目标完成度')),
                      SizedBox(width: 12),
                      Expanded(child: _ChartPlaceholder(title: '词汇量趋势')),
                    ],
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('返回主页'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _ChartPlaceholder extends StatelessWidget {
  final String title;
  const _ChartPlaceholder({required this.title});
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          height: 160,
          decoration: BoxDecoration(
            color: const Color(0xFFF3F4F6),
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        const SizedBox(height: 8),
        Text(title, style: const TextStyle(color: Color(0xFF6B7280))),
      ],
    );
  }
}
