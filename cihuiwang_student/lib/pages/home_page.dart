import 'package:flutter/material.dart';
// import '../pages/learning_page.dart';
import '../widgets/mascot_player.dart';
import '../services/word_parser.dart';
import 'review_quiz_page.dart';
import '../services/srs_store.dart';
import '../services/srs_service.dart';
import '../services/local_store.dart';
import 'book_select_page.dart';
import '../services/prefs_service.dart';
import 'learning_list_page.dart';
import '../widgets/search_widget.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  Future<_ReviewStat> _loadReviewStat() async {
    final due = SrsStore.dueWordKeys().length;
    // 简易：已完成数量从今天的反馈里估计
    final today = DateTime.now();
    final fb = LocalStore.getFeedback();
    int doneToday = 0;
    for (final m in fb) {
      final ts = DateTime.tryParse(m['ts'] ?? '') ?? today;
      if (ts.year == today.year && ts.month == today.month && ts.day == today.day) {
        doneToday++;
      }
    }
    return _ReviewStat(due: due, doneToday: doneToday);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('词汇汪')),
             body: SingleChildScrollView(
         padding: const EdgeInsets.symmetric(horizontal: 20),
         child: Column(
           children: [
             const SizedBox(height: 24),
             // 顶部动画区域
             Center(
               child: ClipRRect(
                 borderRadius: BorderRadius.circular(16),
                 child: const SizedBox(
                   width: 190,
                   height: 190,
                   child: MascotPlayer(
                     asset: 'assets/animations/mainpage.gif',
                     size: 190,
                   ),
                 ),
               ),
             ),
             const SizedBox(height: 16),
             // 问候语区域
             const Text(
               '早安，继续加油！',
               style: TextStyle(
                 fontSize: 22, 
                 fontWeight: FontWeight.w700, 
                 height: 1.2,
               ),
             ),
             const SizedBox(height: 8),
             Text(
               '今日目标：30 个新词 · 复习 40 个',
               style: TextStyle(
                 fontSize: 15, 
                 color: Colors.grey[600], 
                 height: 1.3,
               ),
             ),
             const SizedBox(height: 24),
             // 搜索框
             const SearchWidget(),
             const SizedBox(height: 24),
             // 主要操作按钮
              SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton(
                  onPressed: () async {
                    try {
                      final book = await PrefsService.getSelectedBook();
                      if (!context.mounted) return;
                      if (book == null) {
                        await Navigator.of(context).push(MaterialPageRoute(builder: (_) => const BookSelectPage()));
                        if (!context.mounted) return;
                      }
                      await Navigator.of(context).push(MaterialPageRoute(builder: (_) => const LearningListPage()));
                    } catch (e) {
                      // 若偏好读取通道未就绪，回退直接到词书选择
                      if (!context.mounted) return;
                      await Navigator.of(context).push(MaterialPageRoute(builder: (_) => const BookSelectPage()));
                    }
                  },
                 style: ElevatedButton.styleFrom(
                   shape: RoundedRectangleBorder(
                     borderRadius: BorderRadius.circular(16),
                   ),
                 ),
                 child: const Text(
                   '开始今日学习',
                   style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                 ),
               ),
             ),
             const SizedBox(height: 50), // 学习按钮和卡片之间的间距
             // 功能卡片区域 - 移动到红框位置
             Row(
               children: [
                  Expanded(
                    child: AspectRatio(
                      aspectRatio: 1.1,
                      child: FutureBuilder<_ReviewStat>(
                        future: _loadReviewStat(),
                        builder: (context, snap) {
                          final stat = snap.data ?? const _ReviewStat(due: 0, doneToday: 0);
                          final progress = stat.due == 0 ? 1.0 : (stat.doneToday / stat.due).clamp(0.0, 1.0);
                          final subtitle = stat.due == 0 ? '今日无待复习' : '待复习 ${stat.due}';
                          return _HomeCard(
                            title: '复习任务',
                            subtitle: subtitle,
                            progress: progress,
                            badgeText: '${stat.due}',
                            onTap: () async {
                              // 根据 SRS 到期集合进入经典复习；若无则构造测试集
                              final dueKeys = SrsStore.dueWordKeys();
                              final list = await WordParserService.parseCet4Edited();
                              List<ParsedWord> words;
                              if (dueKeys.isEmpty) {
                                final sample = list.take(30).toList();
                                final now = DateTime.now();
                                for (final w in sample) {
                                  final q = [0,4,5][DateTime.now().millisecond % 3];
                                  final rec = SrsService.updateByQuality(SrsRecord(nextReviewDate: now.subtract(const Duration(days: 1))), q);
                                  await SrsStore.upsertRecord(w.word, rec);
                                }
                                words = sample;
                              } else {
                                final setKeys = dueKeys.toSet();
                                words = list.where((w) => setKeys.contains(w.word)).take(50).toList();
                              }
                              if (!context.mounted) return;
                              Navigator.of(context).push(
                                MaterialPageRoute(builder: (_) => ReviewQuizFlow(words: words)),
                              );
                            },
                          );
                        },
                      ),
                    ),
                  ),
                 const SizedBox(width: 16),
                 Expanded(
                   child: AspectRatio(
                     aspectRatio: 1.1,
                     child: _HomeCard(
                       title: '小组PK',
                       subtitle: '本周进行中',
                       progress: 0.35,
                       onTap: () {},
                     ),
                   ),
                 ),
               ],
             ),
             const SizedBox(height: 24), // 底部留白
           ],
         ),
       ),
    );
  }
}

class _ReviewStat {
  final int due;
  final int doneToday;
  const _ReviewStat({required this.due, required this.doneToday});
}

class _HomeCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final double progress;
  final VoidCallback onTap;
  final String? badgeText;
  const _HomeCard({
    required this.title,
    required this.subtitle,
    required this.progress,
    required this.onTap,
    this.badgeText,
  });
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(18),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // 顶部标题行
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      title,
                      style: const TextStyle(
                        fontWeight: FontWeight.w700,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.black87,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      badgeText ?? '0',
                      style: TextStyle(
                        color: Colors.white, 
                        fontSize: 13,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              // 中间描述文字
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Text(
                  subtitle, 
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                    height: 1.2,
                  ),
                ),
              ),
              // 底部进度条
              ClipRRect(
                borderRadius: BorderRadius.circular(6),
                child: LinearProgressIndicator(
                  value: progress, 
                  minHeight: 8,
                  backgroundColor: Colors.grey[200],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

