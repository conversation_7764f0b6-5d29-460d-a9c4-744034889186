import 'package:flutter/material.dart';
import '../services/local_store.dart';
import '../services/srs_store.dart';

class StudyResultPage extends StatelessWidget {
  final int total; final int learned; final Duration duration; final List<String>? wrongWords; final DateTime? sessionStart;
  const StudyResultPage({super.key, required this.total, required this.learned, required this.duration, this.wrongWords, this.sessionStart});
  @override
  Widget build(BuildContext context) {
    final percent = total == 0 ? 0 : (learned / total * 100).round();
    final nextPlan = _nextReviewSummary();
    return Scaffold(
      appBar: AppBar(title: const Text('学习完成')),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          const SizedBox(height: 12),
          Center(child: Text('完成度 $percent%', style: const TextStyle(fontSize: 24, fontWeight: FontWeight.w800))),
          const SizedBox(height: 8),
          Center(child: Text('用时 ${duration.inMinutes} 分钟', style: const TextStyle(color: Color(0xFF6B7280)))),
          const SizedBox(height: 24),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: const [
                Text('推荐下一步'),
                SizedBox(height: 10),
                Text('· 继续进行“听写复习”巩固刚学单词'),
                Text('· 尝试“短文复习”，在语境中回顾词义'),
              ]),
            ),
          ),
          const SizedBox(height: 12),
          // 近期复习计划摘要
          FutureBuilder<List<String>>(
            future: nextPlan,
            builder: (context, snap) {
              final lines = snap.data ?? const [];
              if (lines.isEmpty) return const SizedBox.shrink();
              return Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                    const Text('近期复习安排'),
                    const SizedBox(height: 8),
                    for (final l in lines) Text(l, style: const TextStyle(color: Color(0xFF6B7280))),
                  ]),
                ),
              );
            },
          ),
          if (wrongWords != null && wrongWords!.isNotEmpty) ...[
            const SizedBox(height: 16),
            const Text('错题列表'),
            const SizedBox(height: 8),
            Expanded(
              child: ListView.separated(
                itemBuilder: (_, i) => Text(wrongWords![i]),
                separatorBuilder: (_, __) => const Divider(height: 8),
                itemCount: wrongWords!.length,
              ),
            ),
          ] else const Spacer(),
          const Spacer(),
          SizedBox(
            width: double.infinity,
            height: 52,
            child: ElevatedButton(onPressed: () => Navigator.popUntil(context, (r) => r.isFirst), child: const Text('返回主页')),
          )
        ]),
      ),
    );
  }

  Future<List<String>> _nextReviewSummary() async {
    // 统计本次会话产生反馈（若有时间戳则按会话起始过滤）
    final records = LocalStore.getFeedback();
    final start = sessionStart;
    final words = <String>{};
    for (final m in records) {
      final tsStr = m['ts'] as String?;
      final w = m['word'] as String?;
      if (w == null || tsStr == null) continue;
      if (start != null) {
        final ts = DateTime.tryParse(tsStr);
        if (ts == null || ts.isBefore(start)) continue;
      }
      words.add(w);
    }
    if (words.isEmpty) return [];
    final buckets = <String, int>{'今天': 0, '明天': 0, '本周': 0, '更晚': 0};
    final now = DateTime.now();
    for (final w in words) {
      final r = SrsStore.getRecord(w);
      final d = r.nextReviewDate;
      if (d.year == now.year && d.month == now.month && d.day == now.day) {
        buckets['今天'] = (buckets['今天'] ?? 0) + 1;
      } else if (d.difference(now).inDays <= 1) {
        buckets['明天'] = (buckets['明天'] ?? 0) + 1;
      } else if (d.difference(now).inDays <= 7) {
        buckets['本周'] = (buckets['本周'] ?? 0) + 1;
      } else {
        buckets['更晚'] = (buckets['更晚'] ?? 0) + 1;
      }
    }
    return buckets.entries.where((e) => e.value > 0).map((e) => '${e.key}：${e.value} 个').toList();
  }
}


