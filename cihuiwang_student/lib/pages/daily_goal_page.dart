import 'package:flutter/material.dart';
import '../services/prefs_service.dart';

class DailyGoalPage extends StatefulWidget {
  const DailyGoalPage({super.key});
  @override
  State<DailyGoalPage> createState() => _DailyGoalPageState();
}

class _DailyGoalPageState extends State<DailyGoalPage> {
  late final TextEditingController _controller;
  int _value = 30;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    PrefsService.getDailyGoal().then((v) {
      _value = v; _controller.text = '$_value';
      if (mounted) setState((){});
    });
  }

  Future<void> _save() async {
    final n = int.tryParse(_controller.text.trim());
    if (n == null || n <= 0) return;
    await PrefsService.setDailyGoal(n);
    if (!mounted) return; Navigator.pop(context, n);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('每日学习目标')),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          const Text('设置每天新学单词数量', style: TextStyle(fontWeight: FontWeight.w700)),
          const SizedBox(height: 12),
          Row(children: [
            IconButton(onPressed: () { final n = (int.tryParse(_controller.text) ?? _value) - 1; if (n>0) setState((){ _controller.text = n.toString();}); }, icon: const Icon(Icons.remove_circle_outline)),
            Expanded(
              child: TextField(
                controller: _controller,
                textAlign: TextAlign.center,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(hintText: '30'),
              ),
            ),
            IconButton(onPressed: () { final n = (int.tryParse(_controller.text) ?? _value) + 1; setState((){ _controller.text = n.toString();}); }, icon: const Icon(Icons.add_circle_outline)),
          ]),
          const SizedBox(height: 8),
          Slider(
            value: double.tryParse(_controller.text) ?? _value.toDouble(),
            min: 5,
            max: 60,
            divisions: 55,
            label: _controller.text,
            onChanged: (v) => setState(() => _controller.text = v.round().toString()),
          ),
          const SizedBox(height: 8),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(onPressed: _save, child: const Text('保存')),
          )
        ]),
      ),
    );
  }
}


