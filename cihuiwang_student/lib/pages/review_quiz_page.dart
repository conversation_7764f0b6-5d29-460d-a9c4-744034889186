import 'dart:math';
import 'package:flutter/material.dart';
import '../services/word_parser.dart';
import '../services/local_store.dart';
import '../services/srs_store.dart';

enum QuizMode { enToCn, cnToEn }

class ReviewQuizPage extends StatefulWidget {
  final List<ParsedWord> words;
  final QuizMode mode;
  const ReviewQuizPage({super.key, required this.words, required this.mode});
  @override
  State<ReviewQuizPage> createState() => _ReviewQuizPageState();
}

class _ReviewQuizPageState extends State<ReviewQuizPage> {
  int _idx = 0;
  // 预留统计（当前未显示，后续可上传/展示）
  // ignore: unused_field
  int _correct = 0;
  final Random _rng = Random();
  final List<ParsedWord> _wrong = [];

  @override
  Widget build(BuildContext context) {
    final w = widget.words[_idx];
    final options = _buildOptions(w);
    return Scaffold(
      appBar: AppBar(title: Text(widget.mode == QuizMode.enToCn ? '选择释义' : '选择单词')),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          LinearProgressIndicator(value: (_idx + 1) / widget.words.length),
          const SizedBox(height: 12),
          if (widget.mode == QuizMode.enToCn)
            Text(w.word, style: const TextStyle(fontSize: 24, fontWeight: FontWeight.w800))
          else
            Text(w.meaning, style: const TextStyle(fontSize: 18)),
          const SizedBox(height: 16),
          for (int i = 0; i < options.length; i++) ...[
            _OptionTile(
              text: options[i].$1,
              isCorrect: options[i].$2,
              onSelected: (ok) {
                if (ok) _correct++;
                final last = _idx == widget.words.length - 1;
                if (!ok) _wrong.add(w);
                setState(() { _idx = (_idx + 1).clamp(0, widget.words.length - 1); });
                if (last) Navigator.pop(context, _wrong);
              },
            ),
            const SizedBox(height: 10),
          ],
          const SizedBox(height: 12),
          // 复习阶段的三态反馈
          Row(children: [
            Expanded(child: _reviewAction('忘记了', const Color(0xFFEF4444), onTap: () { LocalStore.saveFeedback(word: w.word, q: 0, ts: DateTime.now()); SrsStore.applyFeedback(w.word, 0); })),
            const SizedBox(width: 8),
            Expanded(child: _reviewAction('模糊', const Color(0xFFF59E0B), fg: const Color(0xFF111827), onTap: () { LocalStore.saveFeedback(word: w.word, q: 4, ts: DateTime.now()); SrsStore.applyFeedback(w.word, 4); })),
            const SizedBox(width: 8),
            Expanded(child: _reviewAction('记住了', const Color(0xFF22C55E), onTap: () { LocalStore.saveFeedback(word: w.word, q: 5, ts: DateTime.now()); SrsStore.applyFeedback(w.word, 5); })),
          ]),
        ]),
      ),
    );
  }

  List<(String, bool)> _buildOptions(ParsedWord w) {
    // 基于已学习的词集合构造备选，若不足 4 个，则从词书更多词中随机补齐不同项
    final Set<String> used = {};
    final List<(String, bool)> opts = [];
    void addUnique(String text, bool right) {
      if (used.add(text)) opts.add((text, right));
    }

    // 先放正确答案
    if (widget.mode == QuizMode.enToCn) {
      addUnique(w.meaning, true);
      final pool = [...widget.words]..shuffle(_rng);
      for (final p in pool) {
        if (p.word == w.word) continue;
        addUnique(p.meaning, false);
        if (opts.length >= 4) break;
      }
    } else {
      addUnique(w.word, true);
      final pool = [...widget.words]..shuffle(_rng);
      for (final p in pool) {
        if (p.word == w.word) continue;
        addUnique(p.word, false);
        if (opts.length >= 4) break;
      }
    }

    // 如果仍不足 4 项，从内置小词库随机补充不同项（简单静态备选，后续可换成真实词书随机）
    const fallbackWords = ['alpha','bravo','charlie','delta','echo','foxtrot','golf','hotel'];
    fallbackWords.shuffle(_rng);
    for (final fb in fallbackWords) {
      if (opts.length >= 4) break;
      if (widget.mode == QuizMode.enToCn) {
        addUnique('meaning of $fb', false);
      } else {
        addUnique(fb, false);
      }
    }

    // 容错：如果仍不足（极端情况），复制随机项补齐
    while (opts.length < 4 && opts.isNotEmpty) {
      final pick = '${opts[_rng.nextInt(opts.length)].$1} '; // 加空格避免完全重复
      addUnique(pick, false);
    }

    opts.shuffle(_rng);
    return opts;
  }
}

Widget _reviewAction(String text, Color color, {Color? fg, VoidCallback? onTap}) {
  return InkWell(
    onTap: onTap,
    borderRadius: BorderRadius.circular(999),
    child: Container(
      height: 44,
      decoration: BoxDecoration(color: color, borderRadius: BorderRadius.circular(999)),
      alignment: Alignment.center,
      child: Text(text, style: TextStyle(color: fg ?? Colors.white, fontWeight: FontWeight.w700)),
    ),
  );
}

class _OptionTile extends StatefulWidget {
  final String text; final bool isCorrect; final void Function(bool) onSelected;
  const _OptionTile({required this.text, required this.isCorrect, required this.onSelected});
  @override
  State<_OptionTile> createState() => _OptionTileState();
}

class _OptionTileState extends State<_OptionTile> {
  Color _color = Colors.white;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        setState(() { _color = widget.isCorrect ? const Color(0xFFE8F9EE) : const Color(0xFFFFEFEA); });
        widget.onSelected(widget.isCorrect);
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(14),
        decoration: BoxDecoration(
          color: _color,
          border: Border.all(color: const Color(0xFFE5E7EB)),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(widget.text),
      ),
    );
  }
}



class ReviewQuizFlow extends StatefulWidget {
  final List<ParsedWord> words;
  const ReviewQuizFlow({super.key, required this.words});
  @override
  State<ReviewQuizFlow> createState() => _ReviewQuizFlowState();
}

class _ReviewQuizFlowState extends State<ReviewQuizFlow> {
  late List<ParsedWord> _pool;
  List<ParsedWord> _wrongEnToCn = [];
  List<ParsedWord> _wrongCnToEn = [];
  int _stage = 0; // 0 en->cn, 1 错题再刷(en->cn), 2 cn->en, 3 完成

  @override
  void initState() {
    super.initState();
    _pool = [...widget.words];
  }

  Future<void> _startStage(BuildContext context) async {
    if (_stage == 0) {
      final wrong = await Navigator.push<List<ParsedWord>>(context, MaterialPageRoute(
        builder: (_) => ReviewQuizPage(words: _pool, mode: QuizMode.enToCn),
      ));
      _wrongEnToCn = wrong ?? [];
      setState(() => _stage = _wrongEnToCn.isNotEmpty ? 1 : 2);
    } else if (_stage == 1) {
      final wrong = await Navigator.push<List<ParsedWord>>(context, MaterialPageRoute(
        builder: (_) => ReviewQuizPage(words: _wrongEnToCn, mode: QuizMode.enToCn),
      ));
      _wrongEnToCn = wrong ?? [];
      setState(() => _stage = 2);
    } else if (_stage == 2) {
      final wrong = await Navigator.push<List<ParsedWord>>(context, MaterialPageRoute(
        builder: (_) => ReviewQuizPage(words: _pool, mode: QuizMode.cnToEn),
      ));
      _wrongCnToEn = wrong ?? [];
      setState(() => _stage = 3);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_stage < 3) {
      return Scaffold(
        appBar: AppBar(title: const Text('经典复习')),
        body: Center(
          child: SizedBox(
            width: 260,
            child: ElevatedButton(
              onPressed: () => _startStage(context),
              child: Text(_stage == 0 ? '开始：单词选释义' : _stage == 1 ? '错题再刷（单词选释义）' : '中文选单词'),
            ),
          ),
        ),
      );
    }
    final wrongTotal = _wrongEnToCn.length + _wrongCnToEn.length;
    return Scaffold(
      appBar: AppBar(title: const Text('经典复习')),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('复习完成', style: TextStyle(fontSize: 20, fontWeight: FontWeight.w800)),
            const SizedBox(height: 8),
            Text('本次词数：${_pool.length}'),
            const SizedBox(height: 4),
            Text('错题数：$wrongTotal'),
            const Spacer(),
            SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('返回'),
              ),
            )
          ],
        ),
      ),
    );
  }
}
