import 'package:flutter/material.dart';
// MascotPlayer 现在支持 GIF（透明背景）与静态占位
class MascotPlayer extends StatelessWidget {
  final String asset; // 例如 assets/animations/mainpage.gif
  final double? size; // 正方形尺寸（逻辑像素）
  const MascotPlayer({super.key, required this.asset, this.size});

  @override
  Widget build(BuildContext context) {
    final devicePixelRatio = MediaQuery.of(context).devicePixelRatio;
    final target = size != null ? (size! * devicePixelRatio).round() : null;
    final content = Image.asset(
      asset,
      gaplessPlayback: true,
      filterQuality: FilterQuality.low,
      cacheWidth: target,
      cacheHeight: target,
    );
    if (size != null) {
      return SizedBox(
        width: size,
        height: size,
        child: FittedBox(fit: BoxFit.contain, child: content),
      );
    }
    return AspectRatio(aspectRatio: 1, child: Center(child: content));
  }
}


