import 'package:flutter/material.dart';
import '../services/word_api_service.dart';
import '../pages/word_detail_page.dart';

/// 搜索组件
class SearchWidget extends StatefulWidget {
  const SearchWidget({super.key});

  @override
  State<SearchWidget> createState() => _SearchWidgetState();
}

class _SearchWidgetState extends State<SearchWidget> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  List<WordSummaryDto> _searchResults = [];
  bool _isSearching = false;
  bool _showResults = false;
  
  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }
  
  /// 执行搜索
  Future<void> _performSearch(String query) async {
    if (query.trim().isEmpty) {
      setState(() {
        _searchResults = [];
        _showResults = false;
      });
      return;
    }
    
    setState(() {
      _isSearching = true;
      _showResults = true;
    });
    
    try {
      final results = await WordApiService.searchWords(query.trim(), limit: 20);
      if (mounted) {
        setState(() {
          _searchResults = results;
          _isSearching = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _searchResults = [];
          _isSearching = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('搜索失败: $e')),
        );
      }
    }
  }
  
  /// 选择搜索结果
  void _selectResult(WordSummaryDto word) {
    _controller.clear();
    setState(() {
      _showResults = false;
    });
    _focusNode.unfocus();
    
    // 导航到单词详情页
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WordDetailPage(word: word.word),
      ),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 搜索框
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(25),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextField(
            controller: _controller,
            focusNode: _focusNode,
            decoration: InputDecoration(
              hintText: '搜索单词...',
              hintStyle: TextStyle(color: Colors.grey[500]),
              prefixIcon: const Icon(Icons.search, color: Colors.grey),
              suffixIcon: _controller.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear, color: Colors.grey),
                      onPressed: () {
                        _controller.clear();
                        setState(() {
                          _searchResults = [];
                          _showResults = false;
                        });
                      },
                    )
                  : null,
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 20,
                vertical: 15,
              ),
            ),
            onChanged: (value) {
              setState(() {});
              // 防抖搜索
              Future.delayed(const Duration(milliseconds: 500), () {
                if (_controller.text == value) {
                  _performSearch(value);
                }
              });
            },
            onSubmitted: _performSearch,
          ),
        ),
        
        // 搜索结果下拉框
        if (_showResults) ...[
          const SizedBox(height: 8),
          Container(
            constraints: const BoxConstraints(maxHeight: 300),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: _isSearching
                ? const Padding(
                    padding: EdgeInsets.all(20),
                    child: Center(
                      child: CircularProgressIndicator(),
                    ),
                  )
                : _searchResults.isEmpty
                    ? const Padding(
                        padding: EdgeInsets.all(20),
                        child: Center(
                          child: Text(
                            '未找到相关单词',
                            style: TextStyle(color: Colors.grey),
                          ),
                        ),
                      )
                    : ListView.builder(
                        shrinkWrap: true,
                        itemCount: _searchResults.length,
                        itemBuilder: (context, index) {
                          final word = _searchResults[index];
                          return ListTile(
                            leading: Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                color: Colors.blue.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: const Icon(
                                Icons.translate,
                                color: Colors.blue,
                                size: 20,
                              ),
                            ),
                            title: Text(
                              word.word,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (word.phonetic != null) ...[
                                  Text(
                                    '[${word.phonetic}]',
                                    style: TextStyle(
                                      color: Colors.blue[600],
                                      fontSize: 12,
                                    ),
                                  ),
                                  const SizedBox(height: 2),
                                ],
                                Text(
                                  word.meaning,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  style: const TextStyle(fontSize: 13),
                                ),
                              ],
                            ),
                            trailing: word.level != null
                                ? Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 4,
                                    ),
                                    decoration: BoxDecoration(
                                      color: _getLevelColor(word.level!),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Text(
                                      _getLevelText(word.level!),
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 10,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  )
                                : null,
                            onTap: () => _selectResult(word),
                          );
                        },
                      ),
          ),
        ],
      ],
    );
  }
  
  /// 获取级别颜色
  Color _getLevelColor(String level) {
    switch (level.toLowerCase()) {
      case 'core':
        return Colors.red;
      case 'cet4':
        return Colors.blue;
      case 'cet6':
        return Colors.purple;
      case 'toefl':
        return Colors.orange;
      case 'ielts':
        return Colors.green;
      case 'gre':
        return Colors.indigo;
      default:
        return Colors.grey;
    }
  }
  
  /// 获取级别文字
  String _getLevelText(String level) {
    switch (level.toLowerCase()) {
      case 'core':
        return '核心';
      case 'cet4':
        return '四级';
      case 'cet6':
        return '六级';
      case 'toefl':
        return '托福';
      case 'ielts':
        return '雅思';
      case 'gre':
        return 'GRE';
      case 'junior':
        return '中考';
      case 'senior':
        return '高考';
      default:
        return level.toUpperCase();
    }
  }
}
