import '../models/word.dart';

/// 数据仓库占位：后续替换为本地数据库（Hive/SQLite）+ 云端同步（REST）。
class WordRepository {
  Future<List<WordItem>> fetchTodayNewWords() async {
    await Future.delayed(const Duration(milliseconds: 200));
    return [
      WordItem(
        id: '1',
        word: 'canine',
        phonetic: '[ˈkeɪnaɪn]',
        meaning: 'adj. 犬的；犬科的；n. 犬齿',
        example: 'Dogs are canines, and wolves are their wild relatives.',
      ),
    ];
  }
}



