import 'package:shared_preferences/shared_preferences.dart';

class PrefsService {
  static const _keySelectedBook = 'selected_book'; // cet4|cet6
  static const _keyDailyGoal = 'daily_goal';
  static const _keyVoiceType = 'voice_type'; // 0 美音 1 英音

  static Future<void> setSelectedBook(String book) async {
    final p = await SharedPreferences.getInstance();
    await p.setString(_keySelectedBook, book);
  }

  static Future<String?> getSelectedBook() async {
    final p = await SharedPreferences.getInstance();
    return p.getString(_keySelectedBook);
  }

  static Future<void> setDailyGoal(int n) async {
    final p = await SharedPreferences.getInstance();
    await p.setInt(_keyDailyGoal, n);
  }

  static Future<int> getDailyGoal() async {
    final p = await SharedPreferences.getInstance();
    return p.getInt(_keyDailyGoal) ?? 30;
  }

  static Future<void> setVoiceType(int type) async {
    final p = await SharedPreferences.getInstance();
    await p.setInt(_keyVoiceType, type);
  }

  static Future<int> getVoiceType() async {
    final p = await SharedPreferences.getInstance();
    return p.getInt(_keyVoiceType) ?? 0; // 默认美音
  }
}


