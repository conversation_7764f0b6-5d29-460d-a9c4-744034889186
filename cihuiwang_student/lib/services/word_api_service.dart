import 'api_service.dart';

/// 词汇API服务
class WordApiService {
  
  /// 获取今日学习词汇
  static Future<List<WordDetailDto>> getDailyWords({
    int limit = 30,
    String book = 'cet4',
    String level = 'all',
  }) async {
    final response = await ApiService.get('/words/daily', queryParameters: {
      'limit': limit,
      'book': book,
      'level': level,
    });
    
    final List<dynamic> data = response.data;
    return data.map((json) => WordDetailDto.fromJson(json)).toList();
  }
  
  /// 获取单词详情
  static Future<WordDetailDto?> getWordDetail(String word) async {
    try {
      final response = await ApiService.get('/words/detail/$word');
      return WordDetailDto.fromJson(response.data);
    } catch (e) {
      print('获取单词详情失败: $e');
      return null;
    }
  }
  
  /// 批量获取单词详情
  static Future<List<WordDetailDto>> batchGetWords(List<String> words) async {
    final response = await ApiService.post('/words/batch', data: words);
    final List<dynamic> data = response.data;
    return data.map((json) => WordDetailDto.fromJson(json)).toList();
  }
  
  /// 搜索单词
  static Future<List<WordSummaryDto>> searchWords(String query, {int limit = 10}) async {
    final response = await ApiService.get('/words/search', queryParameters: {
      'query': query,
      'limit': limit,
    });
    
    final List<dynamic> data = response.data;
    return data.map((json) => WordSummaryDto.fromJson(json)).toList();
  }
  
  /// 获取词书统计
  static Future<BookStatsDto> getBookStats(String book) async {
    final response = await ApiService.get('/words/stats/$book');
    return BookStatsDto.fromJson(response.data);
  }
  
  /// 测试API连接
  static Future<Map<String, dynamic>> testConnection() async {
    try {
      final response = await ApiService.get('/test/health');
      return {
        'success': true,
        'data': response.data,
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }
}

/// 词汇详细信息DTO
class WordDetailDto {
  final int? id;
  final String word;
  final String? phonetic;
  final String meaning;
  final String? definition;
  final String? pos;
  final int? collins;
  final bool? oxford;
  final String? tag;
  final int? bnc;
  final int? frq;
  final String? exchange;
  final String? level;
  final String? book;
  
  WordDetailDto({
    this.id,
    required this.word,
    this.phonetic,
    required this.meaning,
    this.definition,
    this.pos,
    this.collins,
    this.oxford,
    this.tag,
    this.bnc,
    this.frq,
    this.exchange,
    this.level,
    this.book,
  });
  
  factory WordDetailDto.fromJson(Map<String, dynamic> json) {
    return WordDetailDto(
      id: json['id'],
      word: json['word'] ?? '',
      phonetic: json['phonetic'],
      meaning: json['meaning'] ?? '',
      definition: json['definition'],
      pos: json['pos'],
      collins: json['collins'],
      oxford: json['oxford'],
      tag: json['tag'],
      bnc: json['bnc'],
      frq: json['frq'],
      exchange: json['exchange'],
      level: json['level'],
      book: json['book'],
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'word': word,
      'phonetic': phonetic,
      'meaning': meaning,
      'definition': definition,
      'pos': pos,
      'collins': collins,
      'oxford': oxford,
      'tag': tag,
      'bnc': bnc,
      'frq': frq,
      'exchange': exchange,
      'level': level,
      'book': book,
    };
  }
}

/// 词汇摘要信息DTO
class WordSummaryDto {
  final int? id;
  final String word;
  final String? phonetic;
  final String meaning;
  final String? level;
  
  WordSummaryDto({
    this.id,
    required this.word,
    this.phonetic,
    required this.meaning,
    this.level,
  });
  
  factory WordSummaryDto.fromJson(Map<String, dynamic> json) {
    return WordSummaryDto(
      id: json['id'],
      word: json['word'] ?? '',
      phonetic: json['phonetic'],
      meaning: json['meaning'] ?? '',
      level: json['level'],
    );
  }
}

/// 词书统计信息DTO
class BookStatsDto {
  final String book;
  final int totalWords;
  final int coreWords;
  final int commonWords;
  final int generalWords;
  
  BookStatsDto({
    required this.book,
    required this.totalWords,
    required this.coreWords,
    required this.commonWords,
    required this.generalWords,
  });
  
  factory BookStatsDto.fromJson(Map<String, dynamic> json) {
    return BookStatsDto(
      book: json['book'] ?? '',
      totalWords: json['totalWords'] ?? 0,
      coreWords: json['coreWords'] ?? 0,
      commonWords: json['commonWords'] ?? 0,
      generalWords: json['generalWords'] ?? 0,
    );
  }
}
