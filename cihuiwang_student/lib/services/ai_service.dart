/// AI 服务占位：与教师端/后端衔接时，通过 HTTPS API 或第三方 LLM 提供能力。
/// 未来替换为实际的 API 调用，并加入鉴权与速率控制。
class AiService {
  /// 生成助记（占位实现）
  static Future<String> generateMnemonic(String word) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return 'AI助记：记单词 "$word"，可以联想为中文“看你（$word）”，帮助记忆其与狗相关的含义。';
  }

  /// 生成每日总结（占位实现）
  static Future<String> dailySummary({
    required int learned,
    required List<String> recommendReview,
  }) async {
    await Future.delayed(const Duration(milliseconds: 300));
    return '今天你掌握了 $learned 个新词。建议明天优先复习：${recommendReview.join(', ')}…';
  }

  /// 例句生成（占位）：真实实现建议由服务端 LLM 生成
  static Future<String> exampleSentence(String word) async {
    await Future.delayed(const Duration(milliseconds: 200));
    return 'This is an example sentence using "$word" to help you remember it.';
  }

  /// 生成短文复习（占位）：把词拼接在一段简单语境中
  static Future<String> paragraph(List<String> words) async {
    await Future.delayed(const Duration(milliseconds: 400));
    final joined = words.take(8).join(', ');
    return 'Today\'s mini story uses: $joined. In a small town, students decided to master these words by writing a short diary. As they read aloud, the meanings became clearer.';
  }
}

