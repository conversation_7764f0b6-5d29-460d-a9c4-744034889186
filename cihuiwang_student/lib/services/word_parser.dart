import 'package:flutter/services.dart' show rootBundle;

class ParsedWord {
  final String word;
  final String? phonetic; // 可能为空
  final String meaning;
  ParsedWord({required this.word, this.phonetic, required this.meaning});
}

class WordParserService {
  /// 解析 CET4_edited.txt（含音标），示例行：
  /// abandon [əˈbændən] vt.丢弃；放弃，抛弃
  static Future<List<ParsedWord>> parseCet4Edited() async {
    final text = await rootBundle.loadString('assets/words/CET4_edited.txt');
    final lines = text.split(RegExp(r'\r?\n')).where((l) => l.trim().isNotEmpty).toList();
    final List<ParsedWord> out = [];
    for (final line in lines) {
      // 跳过表头等非词条
      if (RegExp(r'大学英语四级').hasMatch(line)) continue;
      final m = RegExp(r'^(\S+)\s*(?:\[(.*?)\])?\s*(.*)$').firstMatch(line.trim());
      if (m != null) {
        final word = m.group(1)!;
        final phon = m.group(2);
        final rest = m.group(3)!.trim();
        if (word.isNotEmpty && rest.isNotEmpty) {
          out.add(ParsedWord(word: word, phonetic: phon, meaning: rest));
        }
      }
    }
    return out;
  }

  /// 解析 cet6_mixed.txt（多数无音标），列间可能以制表或多个空格分隔：
  /// 形如：competent       adj. ...
  static Future<List<ParsedWord>> parseCet6Mixed() async {
    final text = await rootBundle.loadString('assets/words/cet6_mixed.txt', cache: false);
    final lines = text.split(RegExp(r'\r?\n')).where((l) => l.trim().isNotEmpty).toList();
    final List<ParsedWord> out = [];
    for (final raw in lines) {
      final line = raw.replaceAll('\uFEFF', '').trim();
      if (line.isEmpty) continue;
      // 按制表或连续空白切分
      final parts = line.split(RegExp(r'\s{2,}|\t+'));
      if (parts.isEmpty) continue;
      final word = parts.first.trim();
      // 去除词内可能夹杂的音标方括号
      final cleaned = parts.skip(1).join(' ').replaceAll(RegExp(r'\[.*?\]'), '').trim();
      if (word.isNotEmpty && cleaned.isNotEmpty) {
        out.add(ParsedWord(word: word, phonetic: null, meaning: cleaned));
      }
    }
    return out;
  }
}


