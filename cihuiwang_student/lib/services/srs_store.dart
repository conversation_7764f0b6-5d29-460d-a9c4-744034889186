import 'package:hive_flutter/hive_flutter.dart';
import 'srs_service.dart';

class SrsStore {
  static Box get _box => Hive.box('srs');

  static SrsRecord getRecord(String word) {
    final map = Map<String, dynamic>.from(_box.get(word, defaultValue: <String, dynamic>{}));
    if (map.isEmpty) return SrsRecord();
    return SrsRecord(
      repetitionCount: map['n'] ?? 0,
      easeFactor: (map['ef'] ?? 2.5).toDouble(),
      intervalDays: map['i'] ?? 0,
      nextReviewDate: DateTime.tryParse(map['next'] ?? '') ?? DateTime.now(),
    );
  }

  static Future<void> applyFeedback(String word, int q) async {
    final current = getRecord(word);
    final updated = SrsService.updateByQuality(current, q);
    await _box.put(word, {
      'n': updated.repetitionCount,
      'ef': updated.easeFactor,
      'i': updated.intervalDays,
      'next': updated.nextReviewDate.toIso8601String(),
    });
  }

  // 读取全部记录，便于计算到期词
  static Map<String, SrsRecord> all() {
    final Map<String, SrsRecord> res = {};
    for (final key in _box.keys) {
      final k = key.toString();
      res[k] = getRecord(k);
    }
    return res;
  }

  static List<String> dueWordKeys({DateTime? now}) {
    final t = now ?? DateTime.now();
    final out = <String>[];
    for (final key in _box.keys) {
      final k = key.toString();
      final r = getRecord(k);
      if (!r.nextReviewDate.isAfter(t)) out.add(k);
    }
    return out;
  }

  static Future<void> upsertRecord(String word, SrsRecord record) async {
    await _box.put(word, {
      'n': record.repetitionCount,
      'ef': record.easeFactor,
      'i': record.intervalDays,
      'next': record.nextReviewDate.toIso8601String(),
    });
  }
}


