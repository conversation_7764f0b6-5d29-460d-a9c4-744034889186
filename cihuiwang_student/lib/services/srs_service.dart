/// 改进型 SM-2 SRS 占位实现，用于驱动复习计划。
class SrsRecord {
  int repetitionCount; // n
  double easeFactor; // EF
  int intervalDays; // I
  DateTime nextReviewDate;

  SrsRecord({
    this.repetitionCount = 0,
    this.easeFactor = 2.5,
    this.intervalDays = 0,
    DateTime? nextReviewDate,
  }) : nextReviewDate = nextReviewDate ?? DateTime.now();
}

class SrsService {
  /// q: 0-5，分别映射 忘记(<3)、模糊(4)、记住(5)
  static SrsRecord updateByQuality(SrsRecord record, int q) {
    if (q < 3) {
      record.repetitionCount = 0;
      record.intervalDays = 1;
    } else {
      // EF' = EF + [0.1 - (5 - q) * (0.08 + (5 - q) * 0.02)]
      final delta = 0.1 - (5 - q) * (0.08 + (5 - q) * 0.02);
      record.easeFactor = (record.easeFactor + delta).clamp(1.3, 3.0);
      record.repetitionCount += 1;
      if (record.repetitionCount == 1) {
        record.intervalDays = 1;
      } else if (record.repetitionCount == 2) {
        record.intervalDays = 6;
      } else {
        record.intervalDays = (record.intervalDays * record.easeFactor).round();
      }
    }
    record.nextReviewDate = DateTime.now().add(
      Duration(days: record.intervalDays),
    );
    return record;
  }
}

