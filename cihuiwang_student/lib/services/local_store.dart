import 'package:hive_flutter/hive_flutter.dart';

class LocalStore {
  static Future<void> init() async {
    await Hive.initFlutter();
    await Hive.openBox('study');
    await Hive.openBox('srs');
  }

  static Box get _box => Hive.box('study');

  static Future<void> saveFeedback({required String word, required int q, required DateTime ts}) async {
    final list = List<Map>.from(_box.get('feedback', defaultValue: <Map>[]));
    list.add({'word': word, 'q': q, 'ts': ts.toIso8601String()});
    await _box.put('feedback', list);
  }

  static List<Map> getFeedback() {
    return List<Map>.from(_box.get('feedback', defaultValue: <Map>[]));
  }

  // 简易笔记存取（按单词键存储文本）
  static Future<void> saveNote(String word, String note) async {
    await _box.put('note_$word', note);
  }

  static String getNote(String word) {
    return _box.get('note_$word', defaultValue: '');
  }
}


