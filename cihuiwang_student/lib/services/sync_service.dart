import 'package:connectivity_plus/connectivity_plus.dart';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'word_api_service.dart';
import 'srs_store.dart';
import 'local_store.dart';

/// 数据同步服务
class SyncService {
  static bool _isInitialized = false;
  static bool _isSyncing = false;
  static Timer? _periodicSyncTimer;
  static StreamSubscription? _connectivitySubscription;
  
  static final ValueNotifier<SyncStatus> _syncStatus = ValueNotifier(SyncStatus.idle);
  static ValueNotifier<SyncStatus> get syncStatus => _syncStatus;
  
  /// 初始化同步服务
  static Future<void> init() async {
    if (_isInitialized) return;
    
    // 监听网络状态变化
    _connectivitySubscription = Connectivity().onConnectivityChanged.listen((result) {
      if (result != ConnectivityResult.none) {
        // 网络恢复时自动同步
        _scheduleSync();
      }
    });
    
    // 启动定期同步（每10分钟）
    _periodicSyncTimer = Timer.periodic(const Duration(minutes: 10), (_) {
      _scheduleSync();
    });
    
    _isInitialized = true;
    
    // 初始同步
    _scheduleSync();
  }
  
  /// 销毁同步服务
  static void dispose() {
    _periodicSyncTimer?.cancel();
    _connectivitySubscription?.cancel();
    _isInitialized = false;
  }
  
  /// 手动触发同步
  static Future<void> manualSync() async {
    await _performSync(force: true);
  }
  
  /// 调度同步（防抖）
  static void _scheduleSync() {
    Timer(const Duration(seconds: 2), () {
      _performSync();
    });
  }
  
  /// 执行同步
  static Future<void> _performSync({bool force = false}) async {
    if (_isSyncing && !force) return;
    
    // 检查网络连接
    final connectivity = await Connectivity().checkConnectivity();
    if (connectivity == ConnectivityResult.none) {
      _syncStatus.value = SyncStatus.noNetwork;
      return;
    }
    
    _isSyncing = true;
    _syncStatus.value = SyncStatus.syncing;
    
    try {
      // 1. 同步学习反馈数据
      await _syncFeedbackData();
      
      // 2. 同步SRS记录
      await _syncSrsData();
      
      // 3. 获取服务端更新
      // await _pullServerUpdates();
      
      _syncStatus.value = SyncStatus.success;
      print('数据同步完成');
    } catch (e) {
      _syncStatus.value = SyncStatus.error;
      print('数据同步失败: $e');
    } finally {
      _isSyncing = false;
      
      // 3秒后重置状态
      Timer(const Duration(seconds: 3), () {
        if (_syncStatus.value != SyncStatus.syncing) {
          _syncStatus.value = SyncStatus.idle;
        }
      });
    }
  }
  
  /// 同步学习反馈数据
  static Future<void> _syncFeedbackData() async {
    final feedbacks = LocalStore.getFeedback();
    final unsynced = feedbacks.where((fb) => fb['synced'] != true).toList();
    
    if (unsynced.isEmpty) return;
    
    for (final feedback in unsynced) {
      try {
        // TODO: 调用后端API上传反馈数据
        // await StudyApiService.submitFeedback(feedback);
        
        // 标记为已同步
        feedback['synced'] = true;
      } catch (e) {
        print('同步反馈数据失败: $e');
        rethrow;
      }
    }
    
    // 更新本地存储
    // 注意：这里需要重新保存每个反馈记录
    for (final feedback in feedbacks) {
      if (feedback['synced'] == true) {
        // 重新保存已同步的反馈
        await LocalStore.saveFeedback(
          word: feedback['word'] ?? '',
          q: feedback['q'] ?? 0,
          ts: DateTime.tryParse(feedback['ts'] ?? '') ?? DateTime.now(),
        );
      }
    }
  }
  
  /// 同步SRS记录
  static Future<void> _syncSrsData() async {
    final allRecords = SrsStore.all();
    
    for (final entry in allRecords.entries) {
      try {
        // TODO: 调用后端API上传SRS记录
        // await SrsApiService.updateRecord(entry.key, entry.value);
      } catch (e) {
        print('同步SRS记录失败: ${entry.key} - $e');
        // 继续同步其他记录
      }
    }
  }
  
  /// 检查网络连接
  static Future<bool> checkConnection() async {
    try {
      final result = await WordApiService.testConnection();
      return result['success'] == true;
    } catch (e) {
      return false;
    }
  }
  
  /// 获取同步统计信息
  static Future<SyncStats> getSyncStats() async {
    final feedbacks = LocalStore.getFeedback();
    final unsyncedFeedbacks = feedbacks.where((fb) => fb['synced'] != true).length;
    final srsRecords = SrsStore.all().length;
    
    return SyncStats(
      unsyncedFeedbacks: unsyncedFeedbacks,
      totalSrsRecords: srsRecords,
      lastSyncTime: await _getLastSyncTime(),
      isConnected: await checkConnection(),
    );
  }
  
  /// 获取最后同步时间
  static Future<DateTime?> _getLastSyncTime() async {
    // TODO: 从本地存储获取最后同步时间
    return null;
  }
}

/// 同步状态枚举
enum SyncStatus {
  idle,      // 空闲
  syncing,   // 同步中
  success,   // 成功
  error,     // 错误
  noNetwork, // 无网络
}

/// 同步统计信息
class SyncStats {
  final int unsyncedFeedbacks;
  final int totalSrsRecords;
  final DateTime? lastSyncTime;
  final bool isConnected;
  
  SyncStats({
    required this.unsyncedFeedbacks,
    required this.totalSrsRecords,
    this.lastSyncTime,
    required this.isConnected,
  });
}
