import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// API服务 - 统一的网络请求管理
class ApiService {
  static const String _baseUrl = 'http://localhost:8090/api/v1';
  static const String _tokenKey = 'auth_token';
  
  static late Dio _dio;
  static String? _authToken;
  
  /// 初始化API服务
  static Future<void> init() async {
    _dio = Dio(BaseOptions(
      baseUrl: _baseUrl,
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 10),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));
    
    // 添加请求拦截器
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // 自动添加认证头
        if (_authToken != null) {
          options.headers['Authorization'] = 'Bearer $_authToken';
        }
        print('请求: ${options.method} ${options.path}');
        handler.next(options);
      },
      onResponse: (response, handler) {
        print('响应: ${response.statusCode} ${response.requestOptions.path}');
        handler.next(response);
      },
      onError: (error, handler) async {
        print('网络错误: ${error.message}');
        
        // 401 自动重试逻辑
        if (error.response?.statusCode == 401) {
          // TODO: 实现token刷新逻辑
          print('认证失败，需要重新登录');
        }
        
        handler.next(error);
      },
    ));
    
    // 加载保存的token
    await _loadToken();
  }
  
  /// 加载保存的认证token
  static Future<void> _loadToken() async {
    final prefs = await SharedPreferences.getInstance();
    _authToken = prefs.getString(_tokenKey);
  }
  
  /// 保存认证token
  static Future<void> setAuthToken(String token) async {
    _authToken = token;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, token);
  }
  
  /// 清除认证token
  static Future<void> clearAuthToken() async {
    _authToken = null;
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
  }
  
  /// 是否已认证
  static bool get isAuthenticated => _authToken != null;
  
  /// GET请求
  static Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.get<T>(
        path,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }
  
  /// POST请求
  static Future<Response<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.post<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }
  
  /// PUT请求
  static Future<Response<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.put<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }
  
  /// DELETE请求
  static Future<Response<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
  }) async {
    try {
      return await _dio.delete<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }
  
  /// 处理Dio错误
  static ApiException _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        return ApiException('连接超时，请检查网络设置');
      case DioExceptionType.sendTimeout:
        return ApiException('发送超时，请重试');
      case DioExceptionType.receiveTimeout:
        return ApiException('响应超时，请重试');
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode ?? 0;
        final message = e.response?.data?['message'] ?? '服务器错误';
        return ApiException('服务器错误 ($statusCode): $message');
      case DioExceptionType.cancel:
        return ApiException('请求已取消');
      case DioExceptionType.connectionError:
        return ApiException('网络连接失败，请检查网络设置');
      default:
        return ApiException('未知错误: ${e.message}');
    }
  }
}

/// API异常类
class ApiException implements Exception {
  final String message;
  
  const ApiException(this.message);
  
  @override
  String toString() => 'ApiException: $message';
}
