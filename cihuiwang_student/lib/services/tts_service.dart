import 'package:audioplayers/audioplayers.dart';
import 'dart:io';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:crypto/crypto.dart';
import 'prefs_service.dart';

/// 有道发音 API 播放（无需密钥）。
/// type: 0 美音, 1 英音
class TtsService {
  static final AudioPlayer _player = AudioPlayer();

  static Future<void> play(String word, {int? type}) async {
    final voice = type ?? await PrefsService.getVoiceType();
    final url = 'https://dict.youdao.com/dictvoice?type=$voice&audio=${Uri.encodeComponent(word)}';
    final file = await _cacheFileFor(url);
    await _player.stop();
    if (await file.exists()) {
      await _player.play(DeviceFileSource(file.path));
      return;
    }
    try {
      final resp = await http.get(Uri.parse(url)).timeout(const Duration(seconds: 10));
      if (resp.statusCode == 200) {
        await file.writeAsBytes(resp.bodyBytes, flush: true);
        await _player.play(DeviceFileSource(file.path));
      } else {
        await _player.play(UrlSource(url));
      }
    } catch (_) {
      // fallback 网络直连
      try { await _player.play(UrlSource(url)); } catch (_) {}
    }
  }

  /// 例句 TTS（占位，与单词 TTS 复用 youdao 读音并不准确，后续替换服务端 TTS）
  static Future<void> playSentence(String sentence, {int? type}) async {
    final voice = type ?? await PrefsService.getVoiceType();
    final text = Uri.encodeComponent(sentence);
    final url = 'https://dict.youdao.com/dictvoice?type=$voice&audio=$text';
    try { await _player.stop(); await _player.play(UrlSource(url)); } catch (_) {}
  }

  static Future<File> _cacheFileFor(String url) async {
    final base = await getTemporaryDirectory();
    final dir = Directory('${base.path}/tts_cache');
    if (!await dir.exists()) await dir.create(recursive: true);
    final hash = md5.convert(utf8.encode(url)).toString();
    return File('${dir.path}/$hash.mp3');
  }

  /// 简单的缓存清理：最多保留 200 个文件，按修改时间淘汰；超过 30 天的文件直接删除
  static Future<void> maintainCache() async {
    final base = await getTemporaryDirectory();
    final dir = Directory('${base.path}/tts_cache');
    if (!await dir.exists()) return;
    final files = await dir.list().where((e) => e is File).cast<File>().toList();
    // 删除过期
    final now = DateTime.now();
    for (final f in files) {
      final stat = await f.stat();
      if (now.difference(stat.modified).inDays > 30) {
        try { await f.delete(); } catch (_) {}
      }
    }
    // 按大小控制数量
    final left = await dir.list().where((e) => e is File).cast<File>().toList();
    if (left.length <= 200) return;
    left.sort((a,b) => a.statSync().modified.compareTo(b.statSync().modified));
    for (int i=0; i< left.length - 200; i++) {
      try { await left[i].delete(); } catch (_) {}
    }
  }
}


