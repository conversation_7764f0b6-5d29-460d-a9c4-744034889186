import 'package:flutter/material.dart';
import 'theme/app_theme.dart';
import 'pages/login_page.dart';
import 'widgets/app_tab_scaffold.dart';

import 'package:shared_preferences/shared_preferences.dart';
import 'services/local_store.dart';
import 'services/api_service.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  // 预热 SharedPreferences，避免首次通道未建立导致异常
  try { await SharedPreferences.getInstance(); } catch (_) {}
  await LocalStore.init();
  await ApiService.init();
  runApp(const CihuiWangApp());
}

class CihuiWangApp extends StatelessWidget {
  const CihuiWangApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '词汇汪',
      debugShowCheckedModeBanner: false,
      theme: buildAppTheme(),
      initialRoute: '/login',
      routes: {
        '/login': (_) => const LoginPage(),
        '/home': (_) => const AppTabScaffold(),
      },
    );
  }
}
